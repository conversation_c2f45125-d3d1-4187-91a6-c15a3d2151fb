---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: billing
  annotations:
    allowedEnvironments: dev, staging, qa, production, clone
  namespace: <environment>
spec:
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: billing
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: billing
    spec:
      containers:
        - name: sqs
          image: registry.imtins.com:5005/imt-platform/services/billing:<environment>
          args: ["python manage.py consume"]
          command: ["/bin/sh","-c"]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              memory: 500Mi
            requests:
              cpu: 50m
              memory: 50Mi
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: true
          stdin: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          tty: true
        - name: majesco
          image: registry.imtins.com:5005/imt-platform/services/billing:<environment>
          args: ["python manage.py consume_create_majesco_account_queue"]
          command: ["/bin/sh","-c"]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              memory: 500Mi
            requests:
              cpu: 50m
              memory: 50Mi
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: true
          stdin: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          tty: true
        - name: billing
          image: registry.imtins.com:5005/imt-platform/services/billing:<environment>
          args: ["gunicorn project.wsgi -b 0.0.0.0"]
          command: ["/bin/sh","-c"]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 8000
            timeoutSeconds: 25
          ports:
            - name: 8000tcp02
              containerPort: 8000
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 8000
            timeoutSeconds: 25
          resources:
            limits:
              ephemeral-storage: 150M
              memory: 500Mi
            requests:
              cpu: 50m
              memory: 50Mi
          securityContext:
            runAsNonRoot: true
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      initContainers:
        - name: migrations
          image: registry.imtins.com:5005/imt-platform/services/billing:<environment>
          command:  ["sh", "-c", "if [ \"$ENVIRONMENT\" != \"clone\" ]; then python manage.py migrate; fi"]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
            - name: ENVIRONMENT
              value: "<environment>"
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 150M
              memory: 500Mi
            requests:
              memory: 100Mi
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: billing
          maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: billing
  namespace: <environment>
spec:
  ingressClassName: "nginx"
  rules:
    - host: billing-<environment>.imtins.com
      http:
        paths:
          - backend:
              service:
                name: billing80
                port:
                  number: 80
            path: /
            pathType: ImplementationSpecific
          - backend:
              service:
                name: billing
                port:
                  number: 8000
            path: /api
            pathType: ImplementationSpecific
          - backend:
              service:
                name: billing
                port:
                  number: 8000
            path: /assets
            pathType: ImplementationSpecific
          - backend:
              service:
                name: billing
                port:
                  number: 8000
            path: /health-check
            pathType: ImplementationSpecific
---
apiVersion: v1
kind: Service
metadata:
  name: billing80
  namespace: <environment>
spec:
  ports:
    - port: 80
  selector:
    app: billing
---
apiVersion: v1
kind: Service
metadata:
  name: billing
  namespace: <environment>
spec:
  ports:
    - port: 8000
  selector:
    app: billing