---
apiVersion: v1
kind: Pod
metadata:
  name: nginx-example
spec:
  containers:
    - name: nginx-example
      image: nginx
      imagePullPolicy: Always
      securityContext:
        allowPrivilegeEscalation: false
        capabilities: {}
        privileged: false
        readOnlyRootFilesystem: false
        runAsNonRoot: false
      stdin: true
      tty: true
  restartPolicy: Always
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: nginx-example
  annotations:
    kubernetes.io/ingress.class: nginx
  namespace: default
spec:
  rules:
    - host: nginx-example.imtins.com
      http:
        paths:
          - backend:
              service:
                name: nginx-example
                port:
                  number: 80
            path: /
            pathType: ImplementationSpecific
---
apiVersion: v1
kind: Service
metadata:
  name: nginx-example
  namespace: default
spec:
  ports:
    - port: 80
  selector:
    app: nginx-example