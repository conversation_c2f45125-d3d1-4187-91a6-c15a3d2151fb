---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: automated-adjustor-admin-pdb
  namespace: production
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: automated-adjustor-admin
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: trove-pdb
  namespace: production
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: trove
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: website-pdb
  namespace: production
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: website
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: reminders-remarks-pdb
  namespace: production
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: reminders-remarks
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: projectrequestsystem-pdb
  namespace: production
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: projectrequestsystem
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: my-policy-plus-pdb
  namespace: production
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: my-policy-plus
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: agentdash-pdb
  namespace: production
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: agentdash
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: billing-pdb
  namespace: production
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: billing
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: users-pdb
  namespace: production
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: users
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: service-tracker-pdb
  namespace: production
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: service-tracker
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: filestore-pdb
  namespace: production
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: filestore