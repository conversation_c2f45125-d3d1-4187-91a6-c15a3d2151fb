---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: agentdash-pdb
  namespace: clone
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: agentdash
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: api-pdb
  namespace: clone
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: api
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: behandle-pdb
  namespace: clone
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: behandle
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: behandle-processing-pdb
  namespace: clone
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: behandle-processing
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: billing-pdb
  namespace: clone
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: billing
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: business-rules-pdb
  namespace: clone
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: business-rules
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: cms-pdb
  namespace: clone
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: cms
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: filestore-pdb
  namespace: clone
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: filestore
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: forms-pdb
  namespace: clone
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: forms
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: redis
  namespace: clone
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: redis
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: mapping-pdb
  namespace: clone
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: mapping
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: numbers-pdb
  namespace: clone
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: numbers
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: paglipat-pdb
  namespace: clone
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: paglipat
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: platform-admin-pdb
  namespace: clone
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: platform-admin
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: service-tracker-pdb
  namespace: clone
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: service-tracker
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: users-pdb
  namespace: clone
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: users
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: vinna-pdb
  namespace: clone
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: vinna
---
