---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: api-pdb
  namespace: <environment>
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: api
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: service-tracker-pdb
  namespace: <environment>
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: service-tracker
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: business-rules-pdb
  namespace: <environment>
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: business-rules
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: reminders-remarks-pdb
  namespace: <environment>
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: reminders-remarks
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: filestore-pdb
  namespace: <environment>
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: filestore
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: oedipus-pdb
  namespace: <environment>
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: oedipus
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: trove-pdb
  namespace: <environment>
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: trove
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: policy-data-pdb
  namespace: <environment>
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: policy-data
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: users-pdb
  namespace: <environment>
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: users
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: numbers-pdb
  namespace: <environment>
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: numbers
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: forms-pdb
  namespace: <environment>
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: forms
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: platform-admin-pdb
  namespace: <environment>
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: platform-admin
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: paglipat-pdb
  namespace: <environment>
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: paglipat
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: billing-pdb
  namespace: <environment>
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: billing
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: vinna-pdb
  namespace: <environment>
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: vinna
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: agentdash-pdb
  namespace: <environment>
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: agentdash
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: mapping-pdb
  namespace: <environment>
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: mapping
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: projectrequestsystem-pdb
  namespace: <environment>
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: projectrequestsystem
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: intranet-pdb
  namespace: <environment>
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: intranet
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: behandle-processing-pdb
  namespace: <environment>
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: behandle-processing
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: cms-pdb
  namespace: <environment>
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: cms
