---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: agentdash-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: agentdash
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: api-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: api
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: automated-adjustor-admin-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: automated-adjustor-admin
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: behandle-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: behandle
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: behandle-processing-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: behandle-processing
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: billing-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: billing
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: board-website-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: board-website
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: business-rules-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: business-rules
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: convergence-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: convergence
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: dashboard-admin-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: dashboard-admin
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: gem-calculation-admin-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: gem-calculation-admin
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: intranet-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: intranet
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: my-policy-plus-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: my-policy-plus
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: projectrequestsystem-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: projectrequestsystem
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: redis-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: redis
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: reminders-remarks-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: reminders-remarks
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: web-admins-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: web-admins
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: cms-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: cms
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: filestore-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: filestore
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: forms-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: forms
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: legacy-rate-mapping-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: legacy-rate-mapping
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: mapping-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: mapping
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: numbers-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: numbers
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: paglipat-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: paglipat
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: platform-admin-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: platform-admin
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: policy-data-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: policy-data
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: policy-data-queues-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: policy-data-queues
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: service-tracker-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: service-tracker
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: users-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: users
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: vinna-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: vinna
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: website-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: website
---
