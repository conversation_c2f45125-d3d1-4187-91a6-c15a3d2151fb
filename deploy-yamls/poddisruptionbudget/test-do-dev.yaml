---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: agentdash-pdb
  namespace: dev
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: agentdash
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: api-pdb
  namespace: dev
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: api
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: behandle-pdb
  namespace: dev
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: behandle
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: behandle-processing-pdb
  namespace: dev
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: behandle-processing
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: billing-pdb
  namespace: dev
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: billing
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: business-rules-pdb
  namespace: dev
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: business-rules
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: cms-pdb
  namespace: dev
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: cms
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: filestore-pdb
  namespace: dev
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: filestore
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: forms-pdb
  namespace: dev
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: forms
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: mapping-pdb
  namespace: dev
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: mapping
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: numbers-pdb
  namespace: dev
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: numbers
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: paglipat-pdb
  namespace: dev
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: paglipat
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: platform-admin-pdb
  namespace: dev
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: platform-admin
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: policy-data-pdb
  namespace: dev
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: policy-data
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: policy-data-queues-pdb
  namespace: staging
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: policy-data-queues
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: service-tracker-pdb
  namespace: dev
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: service-tracker
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: users-pdb
  namespace: dev
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: users
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: vinna-pdb
  namespace: dev
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: vinna
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: redis-pdb
  namespace: dev
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: redis
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: web-admins-pdb
  namespace: dev
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: web-admins
---
