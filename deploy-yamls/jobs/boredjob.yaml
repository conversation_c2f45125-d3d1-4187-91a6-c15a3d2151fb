apiVersion: batch/v1
kind: CronJob
metadata:
  name: bored-restart-cronjob
  namespace: lens-platform
spec:
  schedule: "0 */4 * * *" # Every 4 hours (e.g., 12:00, 4:00, 8:00)
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: deployment-restarter
          containers:
          - name: kubectl
            image: bitnami/kubectl:latest
            command:
            - /bin/sh
            - -c
            - kubectl rollout restart deployment/bored-agent
          restartPolicy: OnFailure