apiVersion: v1
kind: ServiceAccount
metadata:
  name: deployment-restarter
  namespace: lens-platform
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: deployment-restarter-role
  namespace: lens-platform
rules:
  - verbs:
      - '*'
    apiGroups:
      - '*'
    resources:
      - deployments
      - cronjobs
      - configmaps
      - secrets
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: deployment-restarter-binding
  namespace: lens-platform
subjects:
- kind: ServiceAccount
  name: deployment-restarter
  namespace: lens-platform
roleRef:
  kind: Role
  name: deployment-restarter-role
  apiGroup: rbac.authorization.k8s.io