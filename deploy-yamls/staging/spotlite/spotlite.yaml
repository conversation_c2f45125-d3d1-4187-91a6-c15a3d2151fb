---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: spotlite
  namespace: staging
spec:
  replicas: 1
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: spotlite
  template:
    metadata:
      labels:
        app: spotlite
      namespace: staging
    spec:
      containers:
        - name: spotlite
          image: registry.imtins.com:5005/rails-apps/spotlite:staging
          args: ["bundle exec rails s -p 3000 -b 0.0.0.0 -e staging"]
          command: [/bin/sh, -c]
          env:
            - name: RUBYOPT
              value: '--yjit'
            - name: RAILS_MASTER_KEY
              value: eead6102dba17eca4be8b3178725a24a
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 3000
            timeoutSeconds: 25
          ports:
            - name: 3000tcp01
              containerPort: 3000
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 3000
            timeoutSeconds: 25
          resources:
            limits:
              memory: 615Mi
            requests:
              cpu: 50m
              memory: 245Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
            privileged: false
            readOnlyRootFilesystem: false
            runAsGroup: 1000
            runAsNonRoot: true
            runAsUser: 1000
            seccompProfile:
              type: RuntimeDefault
          volumeMounts:
            - name: public
              mountPath: /usr/src/app/public
        - name: nginx-static-files
          image: registry.imtins.com:5005/rails-apps/nginx-rails:latest
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 80
            timeoutSeconds: 25
          ports:
            - name: 80tcp01
              containerPort: 80
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 80
            timeoutSeconds: 25
          resources:
            limits:
              memory: 100Mi
            requests:
              cpu: 50m
              memory: 50Mi
          securityContext:
            allowPrivilegeEscalation: false
          volumeMounts:
            - name: public
              mountPath: /usr/src/app/public
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      initContainers:
        - name: migrations
          image: registry.imtins.com:5005/rails-apps/spotlite:staging
          args: ["bin/rails db:migrate"]
          command: [/bin/sh, -c]
          env:
            - name: RAILS_ENV
              value: staging
            - name: RAILS_MASTER_KEY
              value: eead6102dba17eca4be8b3178725a24a
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 500M
              memory: 500Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
            seccompProfile:
              type: RuntimeDefault
        - name: precompile
          image: registry.imtins.com:5005/rails-apps/spotlite:staging
          args: ["bin/rails assets:precompile"]
          command: [/bin/sh, -c]
          env:
            - name: RAILS_ENV
              value: staging
            - name: RAILS_MASTER_KEY
              value: eead6102dba17eca4be8b3178725a24a
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 500M
              memory: 500Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
            seccompProfile:
              type: RuntimeDefault
          volumeMounts:
            - name: public
              mountPath: /usr/src/app/public
      restartPolicy: Always
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: spotlite
          maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
      volumes:
        - name: public
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: spotlite
  annotations:
    kubernetes.io/ingress.class: nginx
  namespace: staging
spec:
  rules:
    - host: spotlite-staging.imtins.com
      http:
        paths:
          - backend:
              service:
                name: spotlite3000
                port:
                  number: 3000
            path: /
            pathType: ImplementationSpecific
    - host: spotlite-staging.imtins.com
      http:
        paths:
          - backend:
              service:
                name: spotlite80
                port:
                  number: 80
            path: /assets
            pathType: ImplementationSpecific
    - host: spotlite-staging.imtins.com
      http:
        paths:
          - backend:
              service:
                name: spotlite80
                port:
                  number: 80
            path: /packs
            pathType: ImplementationSpecific
---
apiVersion: v1
kind: Service
metadata:
  name: spotlite80
  namespace: staging
spec:
  ports:
    - port: 80
  selector:
    app: spotlite
---
apiVersion: v1
kind: Service
metadata:
  name: spotlite3000
  namespace: staging
spec:
  ports:
    - port: 3000
  selector:
    app: spotlite