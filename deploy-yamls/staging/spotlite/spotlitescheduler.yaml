apiVersion: batch/v1
kind: CronJob
metadata:
  name: spotlitescheduler
  namespace: staging
spec:
  concurrencyPolicy: Allow
  failedJobsHistoryLimit: 1
  jobTemplate:
    metadata:
      creationTimestamp: null
    spec:
      template:
        spec:
          containers:
          - args:
            - rails
            - runner
            - -e
            - staging
            - Event.grab
            env:
            - name: RAILS_MASTER_KEY
              value: eead6102dba17eca4be8b3178725a24a
            image: registry.imtins.com:5005/rails-apps/spotlite:staging
            imagePullPolicy: Always
            name: spotlitescheduler
            resources: {}
            securityContext:
              allowPrivilegeEscalation: false
              capabilities: {}
              privileged: false
              readOnlyRootFilesystem: false
              runAsNonRoot: true
            stdin: true
            terminationMessagePath: /dev/termination-log
            terminationMessagePolicy: File
            tty: true
          dnsPolicy: ClusterFirst
          imagePullSecrets:
          - name: gitlab-bot
          restartPolicy: Never
          schedulerName: default-scheduler
          securityContext: {}
          terminationGracePeriodSeconds: 30
  schedule: '*/5 * * * *'
  successfulJobsHistoryLimit: 3
  suspend: false

