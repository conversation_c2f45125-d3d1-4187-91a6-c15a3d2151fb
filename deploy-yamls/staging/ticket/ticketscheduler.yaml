apiVersion: batch/v1
kind: CronJob
metadata:
  name: ticketscheduler
  namespace: staging
spec:
  concurrencyPolicy: Allow
  failedJobsHistoryLimit: 1
  jobTemplate:
    metadata:
      creationTimestamp: null
    spec:
      template:
        spec:
          containers:
          - args:
            - rails
            - runner
            - -e
            - staging
            - Endpoint.sites
            env:
            - name: RAILS_MASTER_KEY
              value: a2753eebfcd26a4f6952bb0dc8314f38
            image: registry.imtins.com:5005/root/trouble-ticket:staging
            imagePullPolicy: Always
            name: ticketscheduler
            resources: {}
            securityContext:
              allowPrivilegeEscalation: false
              capabilities: {}
              privileged: false
              readOnlyRootFilesystem: false
              runAsNonRoot: true
            stdin: true
            terminationMessagePath: /dev/termination-log
            terminationMessagePolicy: File
            tty: true
          dnsPolicy: ClusterFirst
          imagePullSecrets:
          - name: gitlab-bot
          restartPolicy: Never
          schedulerName: default-scheduler
          securityContext: {}
          terminationGracePeriodSeconds: 30
  schedule: '*/5 * * * *'
  successfulJobsHistoryLimit: 3
  suspend: false

