apiVersion: v1
kind: Service
metadata:
  name: ticket80
  labels:
    app.kubernetes.io/name: ticket80
  namespace: staging
spec:
  ports:
  - port: 80
    targetPort: 80
  selector:
    app: ticket
---
apiVersion: v1
kind: Service
metadata:
  name: ticket3000
  labels:
    app.kubernetes.io/name: ticket3000
  namespace: staging
spec:
  ports:
  - port: 3000
    targetPort: 3000
  selector:
    app: ticket
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: ticket
  namespace: staging
spec:
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: ticket
  template:
    metadata:
      labels:
        app: ticket
    spec:
      containers:
      - args:
        - bundle
        - exec
        - rails
        - s
        - -p
        - "3000"
        - -b
        - 0.0.0.0
        - -e
        - staging
        env:
        - name: REDIS_URL
          value: redis://redis:6379
        - name: RAILS_MASTER_KEY
          value: a2753eebfcd26a4f6952bb0dc8314f38
        - name: RUBYOPT
          value: --yjit
        image: registry.digitalocean.com/imt-registry/trouble-ticket:staging
        imagePullPolicy: Always
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 10
          periodSeconds: 120
          successThreshold: 1
          tcpSocket:
            port:3000
          timeoutSeconds: 25
        readinessProbe:
          failureThreshold: 3
          initialDelaySeconds: 10
          periodSeconds: 120
          successThreshold: 1
          tcpSocket:
            port: 3000
          timeoutSeconds: 25
        name: ticket
        ports:
        - containerPort: 3000
          name: 3000tcp01
          protocol: TCP
        securityContext:
          seccompProfile:
            type: RuntimeDefault
          allowPrivilegeEscalation: false
          privileged: false
          readOnlyRootFilesystem: false
          runAsNonRoot: true
        volumeMounts:
        - mountPath: /usr/src/app/public
          name: public
      - args:
        - bundle
        - exec
        - sidekiq
        - -C
        - config/sidekiq.yml
        - -e
        - staging
        env:
        - name: REDIS_URL
          value: redis://redis:6379
        - name: RAILS_MASTER_KEY
          value: a2753eebfcd26a4f6952bb0dc8314f38
        image: registry.digitalocean.com/imt-registry/trouble-ticket:staging
        securityContext:
          seccompProfile:
            type: RuntimeDefault
          allowPrivilegeEscalation: false
          privileged: false
          readOnlyRootFilesystem: false
          runAsNonRoot: true
        imagePullPolicy: Always
        name: sidekiq
      - image: registry.digitalocean.com/imt-registry/nginx-rails:latest
        imagePullPolicy: Always
        name: nginx-static-files
        ports:
        - containerPort: 80
          name: 80tcp01
          protocol: TCP
        volumeMounts:
        - mountPath: /usr/src/app/public
          name: public
      dnsPolicy: ClusterFirst
      imagePullSecrets:
      - name: imt-registry
      initContainers:
      - name: data-permission-fix
        image: busybox
        command: ["/bin/chmod","-R","777", "/usr/src/app/public"]
        volumeMounts:
        - mountPath: /usr/src/app/public
          name: public
      - args:
        - bin/rails
        - db:migrate
        env:
        - name: RAILS_ENV
          value: staging
        - name: RAILS_MASTER_KEY
          value: a2753eebfcd26a4f6952bb0dc8314f38
        image: registry.digitalocean.com/imt-registry/trouble-ticket:staging
        imagePullPolicy: Always
        name: migrations
      - args:
        - bin/rails
        - assets:precompile
        env:
        - name: RAILS_ENV
          value: staging
        - name: RAILS_MASTER_KEY
          value: a2753eebfcd26a4f6952bb0dc8314f38
        image: registry.digitalocean.com/imt-registry/trouble-ticket:staging
        imagePullPolicy: Always
        name: precompile
        volumeMounts:
        - mountPath: /usr/src/app/public
          name: public
      restartPolicy: Always
  volumeClaimTemplates:
  - metadata:
      name: public
    spec:
      accessModes:
      - ReadWriteOnce
      resources:
        requests:
          storage: 10Gi
      storageClassName: do-block-storage 
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ticket
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/proxy-body-size: 5120m
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-keepalive-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
  namespace: staging
spec:
  tls:
  - hosts:
    - ticket-staging.imtins.com
    secretName: imtins
  rules:
  - host: ticket-staging.imtins.com
    http:
      paths:
      - backend:
          service:
              name: ticket3000
              port:
                number: 3000
        path: /
        pathType: ImplementationSpecific
  - host: ticket-staging.imtins.com
    http:
      paths:
      - backend:
          service:
            name: ticket80
            port:
              number: 80
        path: /assets
        pathType: ImplementationSpecific
  - host: ticket-staging.imtins.com
    http:
      paths:
      - backend:
          service:
            name: ticket80
            port:
              number: 80
        path: /packs
        pathType: ImplementationSpecific
  - host: ticket-staging.imtins.com
    http:
      paths:
      - backend:
          service:
            name: ticket80
            port:
              number: 80
        path: /uploads
        pathType: ImplementationSpecific