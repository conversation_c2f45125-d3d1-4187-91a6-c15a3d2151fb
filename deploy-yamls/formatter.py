import argparse
import os
import tempfile
from ruamel.yaml import YAM<PERSON>

def process_args(args):
    if isinstance(args, list):
        # Join the list elements into a single string separated by spaces
        joined_args = ' '.join(args)
        # Return as a single string surrounded by double quotes
        return [f'"{joined_args}"']
    return args

def ensure_command_key(container):
    if 'args' in container:
        # Ensure the command key is added if it doesn't already exist
        if 'command' not in container:
            container['command'] = ["/bin/sh", "-c"]

def process_containers(containers):
    for container in containers:
        if 'args' in container:
            container['args'] = process_args(container['args'])
        ensure_command_key(container)

def process_yaml_file(input_file):
    yaml = YAML()
    yaml.preserve_quotes = True  # Preserve quotes in the output if desired
    yaml.default_flow_style = None  # Use block style for YAML

    # Create a temporary file for processing
    with tempfile.NamedTemporaryFile(delete=False, mode='w', newline='', suffix='.yaml') as temp_file:
        temp_file_name = temp_file.name
        
        with open(input_file, 'r') as f:
            documents = list(yaml.load_all(f))
        
        for doc in documents:
            if 'spec' in doc and 'template' in doc['spec'] and 'spec' in doc['spec']['template']:
                template_spec = doc['spec']['template']['spec']
                if 'containers' in template_spec:
                    process_containers(template_spec['containers'])
                if 'initContainers' in template_spec:
                    process_containers(template_spec['initContainers'])
        
        with open(temp_file_name, 'w') as f:
            yaml.dump_all(documents, f)
    
    # Replace the original file with the modified file
    os.replace(temp_file_name, input_file)

def main():
    parser = argparse.ArgumentParser(description='Process a YAML file to format args and add command.')
    parser.add_argument('file', help='Path to the YAML file to process')

    args = parser.parse_args()

    process_yaml_file(args.file)
    with open(args.file, 'r+') as file:
        content = file.read().replace('\'"', '"').replace('"\'', '"')
        file.seek(0)
        file.write(content)
        file.truncate()

if __name__ == "__main__":
    main()