---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: oedipus
  annotations:
    allowedEnvironments: production
  namespace: default
spec:
  progressDeadlineSeconds: 600
  replicas: 2
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: oedipus
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: oedipus
    spec:
      containers:
        - name: oedipus
          image: registry.imtins.com:5005/root/oedipus:latest
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 80
            timeoutSeconds: 25
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 80
            timeoutSeconds: 25
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 500Mi
            requests:
              cpu: 250m
              memory: 50Mi
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: false
          stdin: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          tty: true
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      restartPolicy: Always
      schedulerName: default-scheduler
      terminationGracePeriodSeconds: 30
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: oedipus
          maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: oedipus
  annotations:
    nginx.ingress.kubernetes.io/proxy-connect-timeout: '420'
    nginx.ingress.kubernetes.io/proxy-read-timeout: '420'
    nginx.ingress.kubernetes.io/proxy-send-timeout: '420'
    kubernetes.io/ingress.class: nginx
  namespace: default
spec:
  ingressClassName: "nginx"
  rules:
    - host: oedipus.imtins.com
      http:
        paths:
          - backend:
              service:
                name: oedipus80
                port:
                  number: 80
            path: /
            pathType: ImplementationSpecific
  tls:
    - hosts:
        - oedipus.imtins.com
      secretName: imtins

---
apiVersion: v1
kind: Service
metadata:
  name: oedipus80
  namespace: default
spec:
  ports:
    - port: 80
  selector:
    app: oedipus