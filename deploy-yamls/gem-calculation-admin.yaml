---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gem-calculation-admin
  annotations:
    allowedEnvironments: staging, production, qa
  namespace: <environment>
spec:
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: gem-calculation-admin
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: gem-calculation-admin
    spec:
      containers:
        - name: gem-calculation-admin
          image: registry.imtins.com:5005/django-apps/gem-calculation-admin:<environment>
          args: ["gunicorn project.wsgi -b 0.0.0.0:8000"]
          command: [/bin/sh, -c]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 8000
            timeoutSeconds: 25
          ports:
            - name: 8000tcp02
              containerPort: 8000
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 8000
            timeoutSeconds: 25
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 500Mi
            requests:
              cpu: 50m
              memory: 50Mi
          securityContext:
            runAsNonRoot: true
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      initContainers:
        - name: migrations
          image: registry.imtins.com:5005/django-apps/gem-calculation-admin:<environment>
          args: ["python manage.py migrate --noinput"]
          command: [/bin/sh, -c]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 500Mi
            requests:
              memory: 100Mi
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: gem-calculation-admin
          maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gem-calculation-admin
  annotations:
    kubernetes.io/ingress.class: nginx
  namespace: <environment>
spec:
  rules:
    - host: gem-trip-<environment>.imtins.com
      http:
        paths:
          - backend:
              service:
                name: gem-calculation-admin
                port:
                  number: 8000
            path: /
            pathType: ImplementationSpecific
          - backend:
              service:
                name: gem-calculation-admin
                port:
                  number: 8000
            path: /health-check
            pathType: ImplementationSpecific
---
apiVersion: v1
kind: Service
metadata:
  name: gem-calculation-admin
  namespace: <environment>
spec:
  ports:
    - port: 8000
  selector:
    app: gem-calculation-admin