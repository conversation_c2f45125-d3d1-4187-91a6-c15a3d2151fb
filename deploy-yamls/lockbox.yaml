---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: lockbox
  annotations:
    allowedEnvironments: default
  namespace: default
spec:
  progressDeadlineSeconds: 600
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: lockbox
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: lockbox
    spec:
      containers:
        - name: lockbox
          image: registry.imtins.com:5005/rails-apps/lockbox
          args: ["bundle exec rails s -p 3000 -b 0.0.0.0 -e production"]
          command: [/bin/sh, -c]
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 3000
            timeoutSeconds: 25
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 3000
            timeoutSeconds: 25
          resources:
            limits:
              memory: 500Mi
            requests:
              cpu: 50m
              memory: 80Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
            privileged: false
            readOnlyRootFilesystem: false
            runAsGroup: 1000
            runAsNonRoot: true
            runAsUser: 1000
            seccompProfile:
              type: RuntimeDefault
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          tty: true
        - name: goodjob-runner
          image: registry.imtins.com:5005/rails-apps/lockbox
          args: ["bundle exec good_job start"]
          command: [/bin/sh, -c]
          env:
            - name: RAILS_ENV
              value: production
            - name: GOOD_JOB_PROBE_PORT
              value: '7001'
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 1
            httpGet:
              path: /status/connected
              port: probe-port
            periodSeconds: 10
          ports:
            - name: probe-port
              containerPort: 7001
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: true
            seccompProfile:
              type: RuntimeDefault
          startupProbe:
            failureThreshold: 30
            httpGet:
              path: /status/started
              port: probe-port
            periodSeconds: 10
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      initContainers:
        - name: migrations
          image: registry.imtins.com:5005/rails-apps/lockbox
          args: ["bundle exec rails db:migrate RAILS_ENV=production"]
          command: [/bin/sh, -c]
          imagePullPolicy: Always
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: false
          stdin: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          tty: true
      restartPolicy: Always
      schedulerName: default-scheduler
      terminationGracePeriodSeconds: 30
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: lockbox
          maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: lockbox
  annotations:
    kubernetes.io/ingress.class: nginx
  namespace: default
spec:
  rules:
    - host: lockbox.imtins.com
      http:
        paths:
          - backend:
              service:
                name: lockbox3000
                port:
                  number: 3000
            path: /
            pathType: ImplementationSpecific
  tls:
    - hosts:
        - lockbox.imtins.com
      secretName: imtins
---
apiVersion: v1
kind: Service
metadata:
  name: lockbox3000
  namespace: default
spec:
  ports:
    - port: 3000
  selector:
    app: lockbox