---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: board-website
  annotations:
    allowedEnvironments: dev, staging, qa, production
  namespace: <environment>
spec:
  progressDeadlineSeconds: 600
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: board-website
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: board-website
    spec:
      containers:
        - name: board-website
          image: registry.imtins.com:5005/django-apps/board-website:<environment>
          args: ["python manage.py runserver 0.0.0.0:8000"]
          command: [/bin/sh, -c]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 8000
            timeoutSeconds: 25
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 8000
            timeoutSeconds: 25
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 500Mi
            requests:
              cpu: 50m
              memory: 85Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
            privileged: false
            readOnlyRootFilesystem: false
            runAsGroup: 1000
            runAsNonRoot: true
            runAsUser: 1000
            seccompProfile:
              type: RuntimeDefault
          stdin: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          tty: true
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      initContainers:
        - name: migrations
          image: registry.imtins.com:5005/django-apps/board-website:<environment>
          args: ["python manage.py migrate --noinput"]
          command: [/bin/sh, -c]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 500Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: true
      restartPolicy: Always
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: cms
          maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: board-website
  namespace: <environment>
spec:
  ingressClassName: "nginx"
  rules:
    - host: board-website-<environment>.imtins.com
      http:
        paths:
          - backend:
              service:
                name: board-website8000
                port:
                  number: 8000
            path: /
            pathType: ImplementationSpecific
          - backend:
              service:
                name: board-website8000
                port:
                  number: 8000
            path: /static
            pathType: ImplementationSpecific
---
apiVersion: v1
kind: Service
metadata:
  name: board-website8000
  namespace: <environment>
spec:
  ports:
    - port: 8000
  selector:
    app: board-website