---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pharos
  annotations:
    allowedEnvironments: dev, production
  namespace: <environment>
spec:
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: pharos
  template:
    metadata:
      labels:
        app: pharos
    spec:
      containers:
        - name: pharos
          image: registry.imtins.com:5005/rails-apps/pharos:latest
          args: ["bundle exec rails s -p 3000 -b 0.0.0.0 -e production"]
          command: [/bin/sh, -c]
          env:
            - name: REDIS_URL
              value: redis://localhost:6379
            - name: RUBYOPT
              value: '--yjit'
          imagePullPolicy: Always
          ports:
            - name: 3000tcp01
              containerPort: 3000
              protocol: TCP
          volumeMounts:
            - name: public
              mountPath: /usr/src/app/public
        - name: sidekiq
          image: registry.imtins.com:5005/rails-apps/pharos:latest
          args: ["bundle exec sidekiq -C config/sidekiq.yml -e production"]
          command: [/bin/sh, -c]
          env:
            - name: REDIS_URL
              value: redis://localhost:6379
          imagePullPolicy: Always
        - name: nginx-static-files
          image: registry.imtins.com:5005/rails-apps/nginx-rails:latest
          imagePullPolicy: Always
          ports:
            - name: 80tcp01
              containerPort: 80
              protocol: TCP
          volumeMounts:
            - name: public
              mountPath: /usr/src/app/public
        - name: redis
          image: redis
          imagePullPolicy: Always
          ports:
            - name: redis
              containerPort: 6379
              protocol: TCP
          resources:
            limits:
              cpu: 500m
              ephemeral-storage: 500M
              memory: 128Mi
            requests:
              cpu: 250m
              memory: 64Mi
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      initContainers:
        - name: migrations
          image: registry.imtins.com:5005/rails-apps/pharos:latest
          args: ["bin/rails db:migrate"]
          command: [/bin/sh, -c]
          env:
            - name: RAILS_ENV
              value: production
          imagePullPolicy: Always
          resources:
            limits:
              memory: 500Mi
            requests:
              memory: 100Mi
        - name: precompile
          image: registry.imtins.com:5005/rails-apps/pharos:latest
          args: ["bin/rails assets:precompile"]
          command: [/bin/sh, -c]
          env:
            - name: RAILS_ENV
              value: production
          imagePullPolicy: Always
          resources:
            limits:
              memory: 500Mi
            requests:
              memory: 100Mi
          volumeMounts:
            - name: public
              mountPath: /usr/src/app/public
      restartPolicy: Always
      volumes:
        - name: public
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: pharos
  namespace: <environment>
spec:
  ingressClassName: "nginx"
  rules:
    - host: pharos.imtins.com
      http:
        paths:
          - backend:
              service:
                name: pharos3000
                port:
                  number: 3000
            path: /
            pathType: ImplementationSpecific
    - host: pharos.imtins.com
      http:
        paths:
          - backend:
              service:
                name: pharos80
                port:
                  number: 80
            path: /assets
            pathType: ImplementationSpecific
    - host: pharos.imtins.com
      http:
        paths:
          - backend:
              service:
                name: pharos80
                port:
                  number: 80
            path: /packs
            pathType: ImplementationSpecific
---
apiVersion: v1
kind: Service
metadata:
  name: pharos80
  namespace: <environment>
spec:
  ports:
    - port: 80
      targetPort: 80
  selector:
    app: pharos
---
apiVersion: v1
kind: Service
metadata:
  name: pharos3000
  namespace: <environment>
spec:
  ports:
    - port: 3000
      targetPort: 3000
  selector:
    app: pharos