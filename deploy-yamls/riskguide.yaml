---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: riskguide
  annotations:
    allowedEnvironments: production, qa
  namespace: <environment>
spec:
  progressDeadlineSeconds: 600
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: riskguide
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: riskguide
    spec:
      containers:
        - name: riskguide
          image: registry.imtins.com:5005/imt-programmers/riskguide:<environment>
          env:
            - name: ENVIRONMENT
              value: "<environment>"
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 25
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 25
          resources:
            limits:
              memory: 100Mi
            requests:
              cpu: 50m
              memory: 50Mi
          stdin: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          tty: true
      initContainers:
        - name: migrations
          image: registry.imtins.com:5005/imt-programmers/riskguide:<environment>
          args: ["vendor/bin/phinx migrate"]
          command: [/bin/sh, -c]
          env:
            - name: ENVIRONMENT
              value: "<environment>"
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 500Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: false
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      restartPolicy: Always
      schedulerName: default-scheduler
      terminationGracePeriodSeconds: 30
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: riskguide
          maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: riskguide
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/configuration-snippet: 'add_header X-Frame-Options "SAMEORIGIN" always; add_header X-Content-Type-Options "nosniff" always; add_header Content-Security-Policy "default-src ''self'' data: https://fonts.googleapis.com ''unsafe-inline'' ''unsafe-eval'' https://fonts.gstatic.com https://www.googletagmanager.com https://ipmeta.io https://*.imtins.com https://*.googleapis.com https://code.jquery.com https://secure.gravatar.com https://*.amazonaws.com https://maxcdn.bootstrapcdn.com https://www.google-analytics.com;";'
  namespace: <environment>
spec:
  rules:
    - host: riskguide-<environment>.imtins.com
      http:
        paths:
          - backend:
              service:
                name: riskguide80
                port:
                  number: 8080
            path: /
            pathType: ImplementationSpecific
---
apiVersion: v1
kind: Service
metadata:
  name: riskguide80
  namespace: <environment>
spec:
  ports:
    - port: 8080
  selector:
    app: riskguide