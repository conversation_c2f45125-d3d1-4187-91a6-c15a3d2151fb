---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: agentdash
  annotations:
    allowedEnvironments: dev, staging, qa, clone, production
  namespace: <environment>
spec:
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: agentdash
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: agentdash
    spec:
      containers:
        - name: agentdash
          image: registry.imtins.com:5005/laravel-apps/agentdash:<environment>
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
            - name: TROVE_TOKEN_TEST
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 60
            tcpSocket:
              port: 8080
            timeoutSeconds: 10
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 60
            tcpSocket:
              port: 8080
            timeoutSeconds: 10
          resources:
            limits:
              ephemeral-storage: 500M
              memory: 1000Mi
              cpu: 1000m
            requests:
              cpu: 180m
              memory: 215Mi
          securityContext:
            allowPrivilegeEscalation: true
            capabilities:
              drop:
                - all
            privileged: true
            readOnlyRootFilesystem: false
            runAsGroup: 0
            runAsNonRoot: false
            runAsUser: 0
            seccompProfile:
              type: RuntimeDefault
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      initContainers:
        - name: migrations
          image: registry.imtins.com:5005/laravel-apps/agentdash:<environment>
          args: ["php artisan migrate --force"]
          command: ["/bin/sh","-c"]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
            - name: TROVE_TOKEN_TEST
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 1000Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: true
            privileged: true
            readOnlyRootFilesystem: false
            runAsGroup: 0
            runAsNonRoot: false
            runAsUser: 0
        - name: seed
          image: registry.imtins.com:5005/laravel-apps/agentdash:<environment>
          args: ["php artisan migrate --seed --force"]
          command: ["/bin/sh","-c"]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
            - name: TROVE_TOKEN_TEST
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 500Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: true
            privileged: true
            readOnlyRootFilesystem: false
            runAsGroup: 0
            runAsNonRoot: false
            runAsUser: 0
      restartPolicy: Always
      schedulerName: default-scheduler
      terminationGracePeriodSeconds: 30
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: agentdash
          maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: agentdash
  annotations:
    nginx.ingress.kubernetes.io/configuration-snippet: 'add_header X-Frame-Options "SAMEORIGIN" always; add_header X-Content-Type-Options "nosniff" always; add_header Content-Security-Policy "default-src ''self'' https://*.createsend1.com data: blob: wss://*.zoom.us https://*.zoom.us https://fonts.googleapis.com ''unsafe-inline'' ''unsafe-eval'' https://fonts.gstatic.com https://www.googletagmanager.com https://ipmeta.io https://*.imtins.com https://*.boldchat.com https://*.amazonaws.com https://www.google-analytics.com https://browser-update.org;";'
    nginx.ingress.kubernetes.io/cors-allow-origin: '*'
    nginx.ingress.kubernetes.io/enable-cors: 'true'
    nginx.ingress.kubernetes.io/proxy-body-size: 400m
    nginx.org/client-max-body-size: 400m
  namespace: <environment>
spec:
  ingressClassName: "nginx"
  rules:
    - host: agentdash-<environment>.imtins.com
      http:
        paths:
          - backend:
              service:
                name: agentdash80
                port:
                  number: 8080
            path: /
            pathType: ImplementationSpecific
          - backend:
              service:
                name: agentdash80
                port:
                  number: 8080
            path: /health-check
            pathType: ImplementationSpecific
---
apiVersion: v1
kind: Service
metadata:
  name: agentdash80
  namespace: <environment>
spec:
  ports:
    - port: 8080
  selector:
    app: agentdash