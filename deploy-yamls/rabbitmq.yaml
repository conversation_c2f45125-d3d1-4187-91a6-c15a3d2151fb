---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rabbitmq
  annotations:
    allowedEnvironments: dev, staging, qa, production, clone
  namespace: <environment>
spec:
  replicas: <replica>
  revisionHistoryLimit: 0
  selector:
    matchLabels:
      app: rabbitmq
  template:
    metadata:
      labels:
        app: rabbitmq
    spec:
      containers:
        - name: rabbitmq
          image: rabbitmq:latest
          imagePullPolicy: Always
          ports:
            - name: rabbitmq
              containerPort: 5672
              protocol: TCP
          resources: {}
          securityContext:
            allowPrivilegeEscalation: false
            capabilities: {}
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: false
          stdin: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          tty: true
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  name: rabbitmq
  labels:
    app.kubernetes.io/name: rabbitmq
  namespace: <environment>
spec:
  ports:
    - port: 5672
      targetPort: 5672
  selector:
    app: rabbitmq