---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: healthchecks
  annotations:
    allowedEnvironments: production
  namespace: <environment>
spec:
  progressDeadlineSeconds: 600
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: healthchecks
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: healthchecks
    spec:
      containers:
        - name: healthchecks
          image: healthchecks/healthchecks:latest
          env:
            - name: ALLOWED_HOSTS
              value: healthchecks.imtins.com
            - name: SITE_ROOT
              value: https://healthchecks.imtins.com
            - name: DB
              value: postgres
            - name: DB_HOST
              value: db-postgresql-production-do-user-2355736-0.c.db.ondigitalocean.com
            - name: DB_USER
              value: healthchecks
            - name: DB_PASSWORD
              value: AVNS_u6ikwmR9Xk1DgIYosmv
            - name: DB_PORT
              value: '25060'
            - name: DB_NAME
              value: healthchecks
            - name: DEFAULT_FROM_EMAIL
              value: <EMAIL>
            - name: EMAIL_HOST
              value: smtp.mailgun.org
            - name: EMAIL_HOST_PASSWORD
              value: **************************************************
            - name: EMAIL_HOST_USER
              value: <EMAIL>
            - name: EMAIL_PORT
              value: '587'
            - name: EMAIL_USE_TLS
              value: 'True'
            - name: SECRET_KEY
              value: IHbvGQz5orpN5GsnEMXw9ygVcMVImbEp
            - name: DEBUG
              value: 'False'
            - name: REGISTRATION_OPEN
              value: 'False'
          imagePullPolicy: Always
          resources:
            limits:
              memory: 500Mi
            requests:
              cpu: 50m
              memory: 65Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          tty: true
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      restartPolicy: Always
      schedulerName: default-scheduler
      terminationGracePeriodSeconds: 30
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: healthchecks
          maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: healthchecks
  annotations:
    kubernetes.io/ingress.class: nginx
  namespace: <environment>
spec:
  rules:
    - host: healthchecks.imtins.com
      http:
        paths:
          - backend:
              service:
                name: healthchecks8000
                port:
                  number: 8000
            path: /
            pathType: ImplementationSpecific
---
apiVersion: v1
kind: Service
metadata:
  name: healthchecks8000
  namespace: <environment>
spec:
  ports:
    - port: 8000
  selector:
    app: healthchecks