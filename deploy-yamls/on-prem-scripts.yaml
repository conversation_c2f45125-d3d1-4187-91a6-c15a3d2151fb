---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: on-prem-scripts
  annotations:
    allowedEnvironments: dev, staging, qa, production
  namespace: <environment>
spec:
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: on-prem-scripts
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: on-prem-scripts
    spec:
      containers:
        - name: on-prem-scripts
          image: registry.imtins.com:5005/django-apps/on-prem-scripts:<environment>
          command: ["/bin/sh", "-c", "while true; do sleep 3600; done"]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 500Mi
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot