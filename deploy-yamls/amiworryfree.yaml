---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: amiworryfree
  annotations:
    allowedEnvironments: production
  namespace: <environment>
spec:
  progressDeadlineSeconds: 600
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: amiworryfree
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: amiworryfree
    spec:
      containers:
        - name: amiworryfree
          image: registry.imtins.com:5005/imt-programmers/am-i-worry-free:latest
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 80
            timeoutSeconds: 25
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 80
            timeoutSeconds: 25
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 500Mi
            requests:
              cpu: 50m
              memory: 50Mi
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: false
          stdin: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          tty: true
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      restartPolicy: Always
      schedulerName: default-scheduler
      terminationGracePeriodSeconds: 30
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: amiworryfree
          maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
---
apiVersion: v1
kind: Service
metadata:
  name: amiworryfree80
  namespace: <environment>
spec:
  ports:
    - port: 80
  selector:
    app: amiworryfree
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    nginx.ingress.kubernetes.io/configuration-snippet: 'add_header X-Frame-Options
      "SAMEORIGIN" always; add_header X-Content-Type-Options "nosniff" always; add_header
      Content-Security-Policy "default-src ''self'' data: https://www.gstatic.com
      https://www.google.com https://www.youtube.com https://fonts.googleapis.com
      ''unsafe-inline'' ''unsafe-eval'' https://fonts.gstatic.com https://www.googletagmanager.com
      https://ipmeta.io https://*.imtins.com https://*.googleapis.com https://code.jquery.com
      https://secure.gravatar.com https://*.amazonaws.com https://maxcdn.bootstrapcdn.com
      https://www.google-analytics.com https://www.googletagmanager.com;";'
  name: amiworryfree
  namespace: <environment>
spec:
  ingressClassName: "nginx"
  rules:
  - host: amiworryfree.com
    http:
      paths:
      - backend:
          service:
            name: amiworryfree80
            port:
              number: 80
        path: /
        pathType: ImplementationSpecific
  - host: www.amiworryfree.com
    http:
      paths:
      - backend:
          service:
            name: amiworryfree80
            port:
              number: 80
        path: /
        pathType: ImplementationSpecific
  tls:
  - hosts:
    - amiworryfree.com
    secretName: amiworryfree
