---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: autobot
  annotations:
    allowedEnvironments: dev, staging, qa, production
  namespace: <environment>
spec:
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: autobot
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: autobot
    spec:
      containers:
        - name: autobot
          image: registry.imtins.com:5005/imt-platform/internal-tools/autobot:latest
          args: ["python manage.py runserver 0.0.0.0:8000"]
          command: ["/bin/sh","-c"]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          ports:
            - name: 8000tcp02
              containerPort: 8000
              protocol: TCP
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 500Mi
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      initContainers:
        - name: migrations
          image: registry.imtins.com:5005/imt-platform/internal-tools/autobot:latest
          args: ["python manage.py migrate --noinput"]
          command: ["/bin/sh","-c"]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 500Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: true
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: autobot
  annotations:
    kubernetes.io/ingress.class: nginx
  namespace: <environment>
spec:
  rules:
    - host: autobot-<environment>.imtins.com
      http:
        paths:
          - backend:
              service:
                name: autobot
                port:
                  number: 8000
            path: /
            pathType: ImplementationSpecific
          - backend:
              service:
                name: autobot
                port:
                  number: 8000
            path: /admin
            pathType: ImplementationSpecific
---
apiVersion: v1
kind: Service
metadata:
  name: autobot
  namespace: <environment>
spec:
  ports:
    - port: 8000
      targetPort: 8000
  selector:
    app: autobot