---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: business-rules
  annotations:
    allowedEnvironments: dev, staging, qa, production, clone
  namespace: <environment>
spec:
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: business-rules
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: business-rules
    spec:
      containers:
        - name: business-rules
          image: registry.imtins.com:5005/imt-platform/services/business-rules:<environment>
          args: ["gunicorn project.wsgi -b 0.0.0.0:8000"]
          command: [/bin/sh, -c]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 8000
            timeoutSeconds: 25
          ports:
            - name: 8000tcp02
              containerPort: 8000
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 8000
            timeoutSeconds: 25
          resources:
            limits:
              ephemeral-storage: 175M
              memory: 1Gi
            requests:
              cpu: 50m
              memory: 65Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
            privileged: false
            readOnlyRootFilesystem: false
            runAsGroup: 1000
            runAsNonRoot: true
            runAsUser: 1000
            seccompProfile:
              type: RuntimeDefault
        - name: sqs
          image: registry.imtins.com:5005/imt-platform/services/business-rules:<environment>
          args: ["python manage.py consume_configuration_clone"]
          command: [/bin/sh, -c]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              memory: 750Mi
            requests:
              cpu: 50m
              memory: 50Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
            privileged: false
            readOnlyRootFilesystem: false
            runAsGroup: 1000
            runAsNonRoot: true
            runAsUser: 1000
            seccompProfile:
              type: RuntimeDefault
          stdin: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          tty: true
        - name: nginx-static
          image: registry.imtins.com:5005/imt-platform/nginx-django:latest
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              memory: 100Mi
            requests:
              cpu: 50m
              memory: 50Mi
          volumeMounts:
            - name: static
              mountPath: /static
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      initContainers:
        - name: migrations
          image: registry.imtins.com:5005/imt-platform/services/business-rules:<environment>
          command:  ["sh", "-c", "if [ \"$ENVIRONMENT\" != \"clone\" ]; then python manage.py migrate; fi"]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
            - name: ENVIRONMENT
              value: "<environment>"
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 175M
              memory: 750Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: true
        - name: precompile
          image: registry.imtins.com:5005/imt-platform/services/business-rules:<environment>
          args: ["cp -r vue-app/. /static/"]
          command: [/bin/sh, -c]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 175M
              memory: 750Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: true
          volumeMounts:
            - name: static
              mountPath: /static
      restartPolicy: Always
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: business-rules
          maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
      volumes:
        - name: static
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: business-rules
  namespace: <environment>
spec:
  ingressClassName: "nginx"
  rules:
    - host: business-rules-<environment>.imtins.com
      http:
        paths:
          - backend:
              service:
                name: business-rules80
                port:
                  number: 80
            path: /
            pathType: ImplementationSpecific
          - backend:
              service:
                name: business-rules
                port:
                  number: 8000
            path: /api
            pathType: ImplementationSpecific
          - backend:
              service:
                name: business-rules
                port:
                  number: 8000
            path: /assets
            pathType: ImplementationSpecific
          - backend:
              service:
                name: business-rules
                port:
                  number: 8000
            path: /health-check
            pathType: ImplementationSpecific
---
apiVersion: v1
kind: Service
metadata:
  name: business-rules80
  namespace: <environment>
spec:
  ports:
    - port: 80
  selector:
    app: business-rules
---
apiVersion: v1
kind: Service
metadata:
  name: business-rules
  namespace: <environment>
spec:
  ports:
    - port: 8000
  selector:
    app: business-rules