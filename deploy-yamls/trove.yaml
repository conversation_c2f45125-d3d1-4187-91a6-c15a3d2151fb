---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: trove
  annotations:
    allowedEnvironments: staging, production
  namespace: <environment>
spec:
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: trove
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: trove
    spec:
      containers:
        - name: trove
          image: registry.digitalocean.com/imt-registry/trove:<environment>
          args: ["bundle exec rails s -p 3000 -b 0.0.0.0 -e <environment>"]
          command: [/bin/sh, -c]
          env:
            - name: RAILS_MASTER_KEY
              value: 6b75969b2710760527f1370a3068283b
            - name: RUBYOPT
              value: '--yjit'
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 3000
            timeoutSeconds: 25
          ports:
            - name: 3000tcp01
              containerPort: 3000
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 3000
            timeoutSeconds: 25
          resources:
            limits:
              memory: 615Mi
            requests:
              cpu: 50m
              memory: 245Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
            privileged: false
            readOnlyRootFilesystem: false
            runAsGroup: 1000
            runAsNonRoot: true
            runAsUser: 1000
            seccompProfile:
              type: RuntimeDefault
          volumeMounts:
            - name: public
              mountPath: /usr/src/app/public
        - name: nginx-static-files
          image: registry.digitalocean.com/imt-registry/nginx-rails:latest
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 80
            timeoutSeconds: 25
          ports:
            - name: 80tcp01
              containerPort: 80
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 80
            timeoutSeconds: 25
          resources:
            limits:
              memory: 100Mi
            requests:
              cpu: 50m
              memory: 50Mi
          securityContext:
            allowPrivilegeEscalation: false
          volumeMounts:
            - name: public
              mountPath: /usr/src/app/public
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
        - name: imt-registry
      initContainers:
        - name: migrations
          image: registry.digitalocean.com/imt-registry/trove:<environment>
          args: ["bin/rails db:migrate"]
          command: [/bin/sh, -c]
          env:
            - name: RAILS_ENV
              value: <environment>
            - name: RAILS_MASTER_KEY
              value: 6b75969b2710760527f1370a3068283b
          imagePullPolicy: Always
        - name: precompile
          image: registry.digitalocean.com/imt-registry/trove:<environment>
          args: ["bin/rails assets:precompile"]
          command: [/bin/sh, -c]
          env:
            - name: RAILS_ENV
              value: <environment>
            - name: RAILS_MASTER_KEY
              value: 6b75969b2710760527f1370a3068283b
          imagePullPolicy: Always
          volumeMounts:
            - name: public
              mountPath: /usr/src/app/public
      restartPolicy: Always
      volumes:
        - name: public
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: trove
  annotations:
    kubernetes.io/ingress.class: nginx
  namespace: <environment>
spec:
  rules:
    - host: trove-<environment>.imtins.com
      http:
        paths:
          - backend:
              service:
                name: trove3000
                port:
                  number: 3000
            path: /
            pathType: ImplementationSpecific
    - host: trove-<environment>.imtins.com
      http:
        paths:
          - backend:
              service:
                name: trove80
                port:
                  number: 80
            path: /assets
            pathType: ImplementationSpecific
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: trove
  namespace: <environment>
spec:
  maxreplicas: <replica>0
  metrics:
    - resource:
        name: cpu
        target:
          averageUtilization: 80
          type: Utilization
      type: Resource
    - resource:
        name: memory
        target:
          averageUtilization: 90
          type: Utilization
      type: Resource
  minReplicas: 2
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: trove
---
apiVersion: v1
kind: Service
metadata:
  name: trove80
  namespace: <environment>
spec:
  ports:
    - port: 80
      targetPort: 80
  selector:
    app: trove
---
apiVersion: v1
kind: Service
metadata:
  name: trove3000
  namespace: <environment>
spec:
  ports:
    - port: 3000
      targetPort: 3000
  selector:
    app: trove