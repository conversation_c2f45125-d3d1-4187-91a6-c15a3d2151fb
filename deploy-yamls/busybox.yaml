---
apiVersion: v1
kind: Pod
metadata:
  name: busybox
  annotations:
    allowedEnvironments: dev, staging, qa, production
spec:
  containers:
    - name: busybox
      image: yauritux/busybox-curl
      imagePullPolicy: Always
      securityContext:
        allowPrivilegeEscalation: false
        capabilities: {}
        privileged: false
        readOnlyRootFilesystem: false
        runAsNonRoot: false
      stdin: true
      tty: true
  restartPolicy: Always