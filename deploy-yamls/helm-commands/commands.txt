## Kafka ##
## https://github.com/bitnami/charts/tree/master/bitnami/kafka
helm repo add bitnami https://charts.bitnami.com/bitnami
helm install kafka bitnami/kafka --set persistence.enabled=false --set zookeeper.persistence.enabled=false --namespace=staging
##


## nfs-subdir ##
helm repo add nfs-subdir-external-provisioner https://kubernetes-sigs.github.io/nfs-subdir-external-provisioner/
helm install nfs-subdir-external-provisioner nfs-subdir-external-provisioner/nfs-subdir-external-provisioner \
    --set nfs.server=192.168.10.112 \
    --set nfs.path=/shares/public
##

## Sumologic ##
helm upgrade --install sumologic sumologic/sumologic \
--namespace=sumologic \
--create-namespace \
--set sumologic.accessId=suWdUuKlxAFfDl \
--set sumologic.accessKey=**************************************************************** \
--set sumologic.clusterName=Kubernetes_cluster \
--set sumologic.collectorName=kubernetes \
--set "sumologic.setup.monitors.notificationEmails={<EMAIL>}" \
--set "sumologic.events.persistence.persistentVolume.storageClass=nfs-client" \
--set "fluentd.persistence.storageClass=nfs-client" \
--set "metadata.persistence.storageClass=nfs-client"
##

