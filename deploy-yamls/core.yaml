---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: core
  annotations:
    allowedEnvironments: production
  namespace: <environment>
spec:
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: core
  template:
    metadata:
      labels:
        app: core
    spec:
      containers:
        - name: core
          image: registry.imtins.com:5005/rails-apps/core:latest
          args: ["bundle exec rails s -p 3000 -b 0.0.0.0 -e production"]
          command: ["/bin/sh","-c"]
          env:
            - name: REDIS_URL
              value: redis://localhost:6379
            - name: RUBYOPT
              value: '--yjit'
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 3000
            timeoutSeconds: 25
          ports:
            - name: 3000tcp01
              containerPort: 3000
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 3000
            timeoutSeconds: 25
          resources:
            limits:
              memory: 500Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
            privileged: false
            readOnlyRootFilesystem: false
            runAsGroup: 1000
            runAsNonRoot: true
            runAsUser: 1000
            seccompProfile:
              type: RuntimeDefault
          volumeMounts:
            - name: public
              mountPath: /usr/src/app/public
        - name: sidekiq
          image: registry.imtins.com:5005/rails-apps/core:latest
          args: ["bundle exec sidekiq -C config/sidekiq.yml -e production"]
          command: ["/bin/sh","-c"]
          env:
            - name: REDIS_URL
              value: redis://localhost:6379
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 6379
            timeoutSeconds: 25
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 6379
            timeoutSeconds: 25
          resources:
            limits:
              memory: 500Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
            privileged: false
            readOnlyRootFilesystem: false
            runAsGroup: 1000
            runAsNonRoot: true
            runAsUser: 1000
            seccompProfile:
              type: RuntimeDefault
        - name: nginx-static-files
          image: registry.imtins.com:5005/rails-apps/nginx-rails:latest
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 80
            timeoutSeconds: 25
          ports:
            - name: 80tcp01
              containerPort: 80
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 80
            timeoutSeconds: 25
          resources:
            limits:
              memory: 250Mi
          securityContext:
            allowPrivilegeEscalation: false
          volumeMounts:
            - name: public
              mountPath: /usr/src/app/public
        - name: redis
          image: redis
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 6379
            timeoutSeconds: 25
          ports:
            - name: redis
              containerPort: 6379
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 6379
            timeoutSeconds: 25
          resources:
            limits:
              memory: 250Mi
          securityContext:
            allowPrivilegeEscalation: false
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      initContainers:
        - name: data-permission-fix
          image: busybox
          command: ["/bin/chmod","-R","777", "/usr/src/app/public"]
          volumeMounts:
          - mountPath: /usr/src/app/public
            name: public
        - name: migrations
          image: registry.imtins.com:5005/rails-apps/core:latest
          args: ["bin/rails db:migrate"]
          command: ["/bin/sh","-c"]
          env:
            - name: RAILS_ENV
              value: production
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 500M
              memory: 500Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
        - name: precompile
          image: registry.imtins.com:5005/rails-apps/core:latest
          args: ["bin/rails assets:precompile"]
          command: ["/bin/sh","-c"]
          env:
            - name: RAILS_ENV
              value: production
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 750M
              memory: 500Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
          volumeMounts:
            - name: public
              mountPath: /usr/src/app/public
      restartPolicy: Always
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: core
          maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
      volumes:
        - name: public
          persistentVolumeClaim:
            claimName: core
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: core
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/proxy-body-size: 1024m
  namespace: <environment>
spec:
  rules:
    - host: core.imtins.com
      http:
        paths:
          - backend:
              service:
                name: core3000
                port:
                  number: 3000
            path: /
            pathType: ImplementationSpecific
    - host: core.imtins.com
      http:
        paths:
          - backend:
              service:
                name: core80
                port:
                  number: 80
            path: /system/users/photos
            pathType: ImplementationSpecific
    - host: core.imtins.com
      http:
        paths:
          - backend:
              service:
                name: core80
                port:
                  number: 80
            path: /assets
            pathType: ImplementationSpecific
    - host: core.imtins.com
      http:
        paths:
          - backend:
              service:
                name: core80
                port:
                  number: 80
            path: /photos/thumb
            pathType: ImplementationSpecific
    - host: core.imtins.com
      http:
        paths:
        - backend:
            service:
              name: core80
              port:
                number: 80
          path: /uploads
          pathType: ImplementationSpecific
---
apiVersion: v1
kind: Service
metadata:
  name: core80
  namespace: <environment>
spec:
  ports:
    - port: 80
  selector:
    app: core
---
apiVersion: v1
kind: Service
metadata:
  name: core3000
  namespace: <environment>
spec:
  ports:
    - port: 3000
  selector:
    app: core
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: core-pdb
  namespace: <environment>
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: core