---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: filestore
  annotations:
    allowedEnvironments: dev, staging, qa, production, clone
  namespace: <environment>
spec:
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: filestore
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: filestore
    spec:
      containers:
        - name: filestore
          image: registry.imtins.com:5005/imt-platform/services/filestore:<environment>
          args: ["gunicorn project.wsgi -b 0.0.0.0:8000"]
          command: [/bin/sh, -c]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 8000
            timeoutSeconds: 25
          ports:
            - name: 8000tcp02
              containerPort: 8000
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 8000
            timeoutSeconds: 25
          resources:
            limits:
              ephemeral-storage: 800M
              memory: 1000Mi
            requests:
              cpu: 50m
              memory: 155Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
            privileged: false
            readOnlyRootFilesystem: false
            runAsGroup: 1000
            runAsNonRoot: true
            runAsUser: 1000
            seccompProfile:
              type: RuntimeDefault
        - name: nginx-static
          image: registry.imtins.com:5005/imt-platform/nginx-django:latest
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              memory: 100Mi
            requests:
              cpu: 50m
              memory: 50Mi
          volumeMounts:
            - name: static
              mountPath: /static
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      initContainers:
        - name: migrations
          image: registry.imtins.com:5005/imt-platform/services/filestore:<environment>
          args: ["python manage.py migrate --noinput"]
          command: [/bin/sh, -c]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 500Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: true
        - name: precompile
          image: registry.imtins.com:5005/imt-platform/services/filestore:<environment>
          args: ["cp -r vue-app/. /static/"]
          command: [/bin/sh, -c]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 500Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: true
          volumeMounts:
            - name: static
              mountPath: /static
      restartPolicy: Always
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: filestore
          maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
      volumes:
        - name: static
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: filestore
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 1024m
    nginx.ingress.kubernetes.io/proxy-connect-timeout: '3600'
    nginx.ingress.kubernetes.io/proxy-keepalive-timeout: '3600'
    nginx.ingress.kubernetes.io/proxy-read-timeout: '3600'
    nginx.ingress.kubernetes.io/proxy-send-timeout: '3600'
  namespace: <environment>
spec:
  ingressClassName: "nginx"
  rules:
    - host: filestore-<environment>.imtins.com
      http:
        paths:
          - backend:
              service:
                name: filestore80
                port:
                  number: 80
            path: /
            pathType: ImplementationSpecific
          - backend:
              service:
                name: filestore
                port:
                  number: 8000
            path: /api
            pathType: ImplementationSpecific
          - backend:
              service:
                name: filestore
                port:
                  number: 8000
            path: /health-check
            pathType: ImplementationSpecific
---
apiVersion: v1
kind: Service
metadata:
  name: filestore80
  namespace: <environment>
spec:
  ports:
    - port: 80
  selector:
    app: filestore
---
apiVersion: v1
kind: Service
metadata:
  name: filestore
  namespace: <environment>
spec:
  ports:
    - port: 8000
  selector:
    app: filestore