---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: projectrequestsystem
  annotations:
    allowedEnvironments: dev, staging, qa, production
  namespace: <environment>
spec:
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: projectrequestsystem
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: projectrequestsystem
    spec:
      containers:
        - name: projectrequestsystem
          image: registry.imtins.com:5005/laravel-apps/projectrequestsystem:<environment>
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
            - name: TROVE_TOKEN_TEST
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 25
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 25
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 850Mi
            requests:
              cpu: 80m
              memory: 165Mi
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: false
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      initContainers:
        - name: migrations
          image: registry.imtins.com:5005/laravel-apps/projectrequestsystem:<environment>
          args: ["php artisan migrate --force"]
          command: [/bin/sh, -c]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
            - name: TROVE_TOKEN_TEST
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 500Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: false
      restartPolicy: Always
      schedulerName: default-scheduler
      terminationGracePeriodSeconds: 30
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: projectrequestsystem
          maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: projectrequestsystem
  annotations:
    nginx.ingress.kubernetes.io/configuration-snippet: 'add_header X-Frame-Options "SAMEORIGIN" always; add_header X-Content-Type-Options "nosniff" always; add_header Content-Security-Policy "default-src ''self'' data: http://requests.imtins.com https://requests.imtins.com https://fonts.googleapis.com ''unsafe-inline'' ''unsafe-eval'' https://fonts.gstatic.com https://www.googletagmanager.com https://ipmeta.io https://*.imtins.com https://*.boldchat.com https://*.amazonaws.com https://www.google-analytics.com https://browser-update.org;";'
    nginx.ingress.kubernetes.io/cors-allow-origin: '*'
    nginx.ingress.kubernetes.io/enable-cors: 'true'
    nginx.ingress.kubernetes.io/proxy-body-size: 400m
    nginx.org/client-max-body-size: 400m
  namespace: <environment>
spec:
  ingressClassName: "nginx"
  rules:
    - host: requests-<environment>.imtins.com
      http:
        paths:
          - backend:
              service:
                name: projectrequestsystem80
                port:
                  number: 8080
            path: /
            pathType: ImplementationSpecific
          - backend:
              service:
                name: projectrequestsystem80
                port:
                  number: 8080
            path: /health-check
            pathType: ImplementationSpecific
---
apiVersion: v1
kind: Service
metadata:
  name: projectrequestsystem80
  namespace: <environment>
spec:
  ports:
    - port: 8080
  selector:
    app: projectrequestsystem