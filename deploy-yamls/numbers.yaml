---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: numbers
  annotations:
    allowedEnvironments: dev, staging, qa, production, clone
  namespace: <environment>
spec:
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: numbers
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: numbers
    spec:
      containers:
        - name: numbers
          image: registry.imtins.com:5005/imt-platform/services/numbers:<environment>
          args: ["gunicorn project.wsgi -b 0.0.0.0:8000"]
          command: [/bin/sh, -c]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 8000
            timeoutSeconds: 25
          ports:
            - name: 8000tcp02
              containerPort: 8000
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 8000
            timeoutSeconds: 25
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 500Mi
            requests:
              cpu: 50m
              memory: 50Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
            privileged: false
            readOnlyRootFilesystem: false
            runAsGroup: 1000
            runAsNonRoot: true
            runAsUser: 1000
            seccompProfile:
              type: RuntimeDefault
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      initContainers:
        - name: migrations
          image: registry.imtins.com:5005/imt-platform/services/numbers:<environment>
          command: ["sh", "-c", "if [ \"$ENVIRONMENT\" != \"clone\" ]; then python manage.py migrate; fi"]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
            - name: ENVIRONMENT
              value: "<environment>"
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 500Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: true
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: numbers
          maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: numbers
  namespace: <environment>
spec:
  ingressClassName: "nginx"
  rules:
    - host: numbers-<environment>.imtins.com
      http:
        paths:
          - backend:
              service:
                name: numbers80
                port:
                  number: 80
            path: /
            pathType: ImplementationSpecific
          - backend:
              service:
                name: numbers
                port:
                  number: 8000
            path: /api
            pathType: ImplementationSpecific
          - backend:
              service:
                name: numbers
                port:
                  number: 8000
            path: /health-check
            pathType: ImplementationSpecific
---
apiVersion: v1
kind: Service
metadata:
  name: numbers80
  namespace: <environment>
spec:
  ports:
    - port: 80
  selector:
    app: numbers
---
apiVersion: v1
kind: Service
metadata:
  name: numbers
  namespace: <environment>
spec:
  ports:
    - port: 8000
  selector:
    app: numbers