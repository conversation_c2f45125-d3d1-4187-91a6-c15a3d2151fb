---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: behandle-processing
  annotations:
    allowedEnvironments: dev, staging, qa, production, clone
  namespace: <environment>
spec:
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: behandle-processing
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: behandle-processing
    spec:
      containers:
        - name: behandle-processing
          image: registry.imtins.com:5005/imt-platform/services/behandle:<environment>
          args: [" celery -A project.celery worker -B --loglevel=info"]
          command: ["/bin/sh","-c"]
          env:
            - name: COLUMNS
              value: '20'
            - name: LINES
              value: '5'
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 1500Mi
            requests:
              cpu: 50m
              memory: 375Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
            privileged: false
            readOnlyRootFilesystem: false
            runAsGroup: 1000
            runAsNonRoot: true
            runAsUser: 1000
            seccompProfile:
              type: RuntimeDefault
          stdin: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          tty: true
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: behandle-processing
          maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway