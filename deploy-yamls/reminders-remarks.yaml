---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: reminders-remarks
  annotations:
    allowedEnvironments: staging, qa, production
  namespace: <environment>
spec:
  progressDeadlineSeconds: 600
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: reminders-remarks
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: reminders-remarks
    spec:
      containers:
        - name: reminders-remarks
          image: registry.imtins.com:5005/laravel-apps/reminders-remarks:<environment>
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
            - name: TROVE_TOKEN_TEST
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 25
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 25
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 500Mi
            requests:
              cpu: 50m
              memory: 135Mi
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: false
          stdin: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          tty: true
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      restartPolicy: Always
      schedulerName: default-scheduler
      terminationGracePeriodSeconds: 30
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: reminders-remarks
          maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: reminders-remarks
  namespace: <environment>
spec:
  ingressClassName: "nginx"
  rules:
    - host: reminders-<environment>.imtins.com
      http:
        paths:
          - backend:
              service:
                name: reminders-remarks80
                port:
                  number: 8080
            path: /
            pathType: ImplementationSpecific
---
apiVersion: v1
kind: Service
metadata:
  name: reminders-remarks80
  namespace: <environment>
spec:
  ports:
    - port: 8080
  selector:
    app: reminders-remarks