---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: users
  annotations:
    allowedEnvironments: dev, staging, qa, production, clone
  namespace: <environment>
spec:
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: users
  strategy:
    rollingUpdate:
      maxSurge: 2      # Increased from 1 to 2
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: users
    spec:
      terminationGracePeriodSeconds: 90   # Added termination grace period
      containers:
        - name: sqs
          image: registry.imtins.com:5005/imt-platform/services/users:<environment>
          args: ["python manage.py consume"]
          command: [/bin/sh, -c]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              memory: 500Mi
              ephemeral-storage: 300Mi    # Added ephemeral storage limit
            requests:
              cpu: 50m
              memory: 50Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
            privileged: false
            readOnlyRootFilesystem: false
            runAsGroup: 1000
            runAsNonRoot: true
            runAsUser: 1000
            seccompProfile:
              type: RuntimeDefault
          lifecycle:                       # Added lifecycle hook
            preStop:
              exec:
                command: ["/bin/sh", "-c", "sleep 15"]
          stdin: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          tty: true
        - name: daphne
          image: registry.imtins.com:5005/imt-platform/services/users:<environment>
          args: ["daphne -b 0.0.0.0 -p 8001 project.asgi:application"]
          command: [/bin/sh, -c]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          ports:
            - name: 8001tcp02
              containerPort: 8001
              protocol: TCP
          resources:
            limits:
              memory: 500Mi
              ephemeral-storage: 300Mi    # Added ephemeral storage limit
            requests:
              cpu: 50m
              memory: 55Mi
              ephemeral-storage: 150Mi    # Added ephemeral storage request
          readinessProbe:                 # Added readiness probe
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 20
            successThreshold: 1
            tcpSocket:
              port: 8001
            timeoutSeconds: 5
          livenessProbe:                  # Added liveness probe
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 8001
            timeoutSeconds: 5
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
            privileged: false
            readOnlyRootFilesystem: false
            runAsGroup: 1000
            runAsNonRoot: true
            runAsUser: 1000
            seccompProfile:
              type: RuntimeDefault
          lifecycle:                       # Added lifecycle hook
            preStop:
              exec:
                command: ["/bin/sh", "-c", "sleep 15"]
          stdin: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          tty: true
        - name: users
          image: registry.imtins.com:5005/imt-platform/services/users:<environment>
          args: ["gunicorn project.wsgi -b 0.0.0.0:8000"]
          command: [/bin/sh, -c]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 15       # Increased from 10
            periodSeconds: 30             # Reduced from 120
            successThreshold: 1
            tcpSocket:
              port: 8000
            timeoutSeconds: 5             # Reduced from 25
          ports:
            - name: 8000tcp02
              containerPort: 8000
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 20             # Reduced from 120
            successThreshold: 1
            tcpSocket:
              port: 8000
            timeoutSeconds: 5             # Reduced from 25
          resources:
            limits:
              ephemeral-storage: 400M     # Increased from 200M
              memory: 1000Mi
            requests:
              cpu: 50m
              memory: 65Mi
              ephemeral-storage: 200M     # Added ephemeral storage request
          lifecycle:                       # Added lifecycle hook
            preStop:
              exec:
                command: ["/bin/sh", "-c", "sleep 15"]
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
            privileged: false
            readOnlyRootFilesystem: false
            runAsGroup: 1000
            runAsNonRoot: true
            runAsUser: 1000
            seccompProfile:
              type: RuntimeDefault
        - name: nginx-static
          image: registry.imtins.com:5005/imt-platform/nginx-django:latest
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              memory: 100Mi
              ephemeral-storage: 200Mi    # Added ephemeral storage limit
            requests:
              cpu: 50m
              memory: 50Mi
          lifecycle:                       # Added lifecycle hook
            preStop:
              exec:
                command: ["/bin/sh", "-c", "sleep 15"]
          volumeMounts:
            - name: static
              mountPath: /static
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      initContainers:
        - name: migrations
          image: registry.imtins.com:5005/imt-platform/services/users:<environment>
          args: ["python manage.py migrate --noinput"]
          command: [/bin/sh, -c]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 400M     # Increased from 200M
              memory: 500Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: true
        - name: precompile
          image: registry.imtins.com:5005/imt-platform/services/users:<environment>
          args: ["cp -r vue-app/. /static/"]
          command: [/bin/sh, -c]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 400M     # Increased from 200M
              memory: 500Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: true
          volumeMounts:
            - name: static
              mountPath: /static
      restartPolicy: Always
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: users
          maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
      volumes:
        - name: static
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: users
  annotations:
    nginx.ingress.kubernetes.io/configuration-snippet: 'add_header X-Frame-Options "SAMEORIGIN" always; add_header X-Content-Type-Options "nosniff" always; add_header Content-Security-Policy "default-src ''self'' blob: data: https://o527262.ingest.sentry.io https://fonts.googleapis.com ''unsafe-inline'' ''unsafe-eval'' https://fonts.gstatic.com https://www.googletagmanager.com https://ipmeta.io https://*.imtins.com https://*.boldchat.com https://*.amazonaws.com https://www.google-analytics.com https://browser-update.org;";'
    nginx.ingress.kubernetes.io/proxy-buffer-size: 15k
    nginx.ingress.kubernetes.io/proxy-buffering: 'on'
    nginx.ingress.kubernetes.io/proxy-max-temp-file-size: 1024m
    nginx.ingress.kubernetes.io/proxy-read-timeout: '3600'
    nginx.ingress.kubernetes.io/proxy-send-timeout: '3600'
  namespace: <environment>
spec:
  ingressClassName: "nginx"
  rules:
    - host: users-<environment>.imtins.com
      http:
        paths:
          - backend:
              service:
                name: users80
                port:
                  number: 80
            path: /
            pathType: ImplementationSpecific
          - backend:
              service:
                name: users
                port:
                  number: 8000
            path: /api
            pathType: ImplementationSpecific
          - backend:
              service:
                name: users
                port:
                  number: 8000
            path: /assets
            pathType: ImplementationSpecific
          - backend:
              service:
                name: users
                port:
                  number: 8000
            path: /sso
            pathType: ImplementationSpecific
          - backend:
              service:
                name: users
                port:
                  number: 8000
            path: /health-check
            pathType: ImplementationSpecific
          - backend:
              service:
                name: users8001
                port:
                  number: 8001
            path: /notifications
            pathType: ImplementationSpecific
---
apiVersion: v1
kind: Service
metadata:
  name: users8001
  namespace: <environment>
spec:
  ports:
    - port: 8001
  selector:
    app: users
---
apiVersion: v1
kind: Service
metadata:
  name: users80
  namespace: <environment>
spec:
  ports:
    - port: 80
  selector:
    app: users
---
apiVersion: v1
kind: Service
metadata:
  name: users
  namespace: <environment>
spec:
  ports:
    - port: 8000
  selector:
    app: users