---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: trove-expired-email
  annotations:
    allowedEnvironments: staging, production
  namespace: <environment>
spec:
  concurrencyPolicy: Allow
  failedJobsHistoryLimit: 1
  jobTemplate:
    metadata:
      creationTimestamp: null
    spec:
      template:
        spec:
          containers:
            - name: trove-expired-email
              image: registry.digitalocean.com/imt-registry/trove:<environment>
              args: ["rails runner -e <environment> Token.expired_email"]
              command: [/bin/sh, -c]
              env:
                - name: RAILS_MASTER_KEY
                  value: 6b75969b2710760527f1370a3068283b
                - name: RUBYOPT
                  value: '--yjit'
              imagePullPolicy: Always
              resources: {}
              securityContext:
                allowPrivilegeEscalation: false
                capabilities: {}
                privileged: false
                readOnlyRootFilesystem: false
                runAsNonRoot: true
              stdin: true
              terminationMessagePath: /dev/termination-log
              terminationMessagePolicy: File
              tty: true
          dnsPolicy: ClusterFirst
          imagePullSecrets:
            - name: gitlab-bot
            - name: imt-registry
          restartPolicy: Never
          schedulerName: default-scheduler
          securityContext: {}
          terminationGracePeriodSeconds: 30
  schedule: 0 5 * * *
  successfulJobsHistoryLimit: 3
  suspend: false