---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: imtonline-website
  annotations:
    allowedEnvironments: dev
  namespace: <environment>
spec:
  progressDeadlineSeconds: 600
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: imtonline-website
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: imtonline-website
    spec:
      containers:
        - name: imtonline-website
          image: registry.imtins.com:5005/imt-online/public/public-website:<environment>
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 80
            timeoutSeconds: 25
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 80
            timeoutSeconds: 25
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 100Mi
            requests:
              cpu: 50m
              memory: 50Mi
          stdin: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          tty: true
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      restartPolicy: Always
      schedulerName: default-scheduler
      terminationGracePeriodSeconds: 30
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: imtonline-website
          maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
---
apiVersion: v1
kind: Service
metadata:
  name: imtonline-website80
  namespace: <environment>
spec:
  ports:
    - port: 80
  selector:
    app: imtonline-website