---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: intranet
  annotations:
    allowedEnvironments: staging, qa, production
  namespace: <environment>
spec:
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: intranet
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: intranet
    spec:
      containers:
        - name: intranet
          image: registry.imtins.com:5005/django-apps/intranet:<environment>
          args: ["gunicorn project.wsgi -b 0.0.0.0:8000"]
          command: [/bin/sh, -c]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 8000
            timeoutSeconds: 25
          ports:
            - name: 8000tcp02
              containerPort: 8000
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 8000
            timeoutSeconds: 25
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 500Mi
            requests:
              cpu: 70m
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: true
        - name: nginx-static
          image: registry.imtins.com:5005/imt-platform/nginx-django:latest
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              memory: 100Mi
            requests:
              cpu: 50m
              memory: 50Mi
          volumeMounts:
            - name: static
              mountPath: /static
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      initContainers:
        - name: migrations
          image: registry.imtins.com:5005/django-apps/intranet:<environment>
          args: ["python manage.py migrate --noinput"]
          command: [/bin/sh, -c]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 500Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: true
        - name: setup
          image: registry.imtins.com:5005/django-apps/intranet:<environment>
          args: ["python manage.py setup"]
          command: [/bin/sh, -c]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 500Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: true
        - name: precompile
          image: registry.imtins.com:5005/django-apps/intranet:<environment>
          args: ["cp -r vue-app/. /static/"]
          command: [/bin/sh, -c]
          env:
            - name: TROVE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: trove-token
                  key: token
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 100M
              memory: 500Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: true
          volumeMounts:
            - name: static
              mountPath: /static
      restartPolicy: Always
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: intranet
          maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
      volumes:
        - name: static
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    nginx.ingress.kubernetes.io/configuration-snippet: 'add_header X-Frame-Options
      "SAMEORIGIN" always; add_header X-Content-Type-Options "nosniff" always; add_header
      Content-Security-Policy "default-src ''self'' data: https://o527262.ingest.sentry.io
      https://fonts.googleapis.com ''unsafe-inline'' ''unsafe-eval'' https://fonts.gstatic.com
      https://www.googletagmanager.com https://ipmeta.io https://*.imtins.com https://*.boldchat.com
      https://*.amazonaws.com https://www.google-analytics.com https://browser-update.org
      https://imtinsurance.hrmdirect.com/ https://reports.hrmdirect.com/ https://www.youtube.com
      https://ajax.googleapis.com";'
    nginx.ingress.kubernetes.io/proxy-body-size: 1024m
  name: intranet
  namespace: <environment>
spec:
  ingressClassName: "nginx"
  rules:
  - host: intranet-<environment>.imtins.com
    http:
      paths:
      - backend:
          service:
            name: intranet80
            port:
              number: 80
        path: /
        pathType: ImplementationSpecific
      - backend:
          service:
            name: intranet
            port:
              number: 8000
        path: /api
        pathType: ImplementationSpecific
      - backend:
          service:
            name: intranet
            port:
              number: 8000
        path: /admin
        pathType: ImplementationSpecific
      - backend:
          service:
            name: intranet
            port:
              number: 8000
        path: /media
        pathType: ImplementationSpecific
      - backend:
          service:
            name: intranet
            port:
              number: 8000
        path: /documents
        pathType: ImplementationSpecific
      - backend:
          service:
            name: intranet
            port:
              number: 8000
        path: /static
        pathType: ImplementationSpecific
      - backend:
          service:
            name: intranet
            port:
              number: 8000
        path: /health-check
        pathType: ImplementationSpecific

---
apiVersion: v1
kind: Service
metadata:
  name: intranet80
  namespace: <environment>
spec:
  ports:
    - port: 80
  selector:
    app: intranet
---
apiVersion: v1
kind: Service
metadata:
  name: intranet
  namespace: <environment>
spec:
  ports:
    - port: 8000
  selector:
    app: intranet