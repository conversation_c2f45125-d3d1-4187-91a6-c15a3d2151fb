---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: feemon
  annotations:
    allowedEnvironments: production
  namespace: <environment>
spec:
  replicas: <replica>
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: feemon
  template:
    metadata:
      labels:
        app: feemon
      namespace: <environment>
    spec:
      containers:
        - name: feemon
          image: registry.imtins.com:5005/rails-apps/feemon:latest
          args: ["bundle exec rails s -p 3000 -b 0.0.0.0 -e production"]
          command: [/bin/sh, -c]
          env:
            - name: RUBYOPT
              value: '--yjit'
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 3000
            timeoutSeconds: 25
          ports:
            - name: 3000tcp01
              containerPort: 3000
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 3000
            timeoutSeconds: 25
          resources:
            limits:
              memory: 615Mi
            requests:
              cpu: 50m
              memory: 245Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
            privileged: false
            readOnlyRootFilesystem: false
            runAsGroup: 1000
            runAsNonRoot: true
            runAsUser: 1000
            seccompProfile:
              type: RuntimeDefault
          volumeMounts:
            - name: public
              mountPath: /usr/src/app/public
        - name: nginx-static-files
          image: registry.imtins.com:5005/rails-apps/nginx-rails:latest
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 80
            timeoutSeconds: 25
          ports:
            - name: 80tcp01
              containerPort: 80
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            tcpSocket:
              port: 80
            timeoutSeconds: 25
          resources:
            limits:
              memory: 100Mi
            requests:
              cpu: 50m
              memory: 50Mi
          securityContext:
            allowPrivilegeEscalation: false
          volumeMounts:
            - name: public
              mountPath: /usr/src/app/public
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: gitlab-bot
      initContainers:
        - name: migrations
          image: registry.imtins.com:5005/rails-apps/feemon:latest
          args: ["bin/rails db:migrate"]
          command: [/bin/sh, -c]
          env:
            - name: RAILS_ENV
              value: production
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 500M
              memory: 500Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
            seccompProfile:
              type: RuntimeDefault
        - name: yarn
          image: registry.imtins.com:5005/rails-apps/feemon:latest
          args: ["yarn install"]
          command: [/bin/sh, -c]
          env:
            - name: RAILS_ENV
              value: production
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 500M
              memory: 500Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
            seccompProfile:
              type: RuntimeDefault
        - name: precompile
          image: registry.imtins.com:5005/rails-apps/feemon:latest
          args: ["bin/rails assets:precompile"]
          command: [/bin/sh, -c]
          env:
            - name: RAILS_ENV
              value: production
          imagePullPolicy: Always
          resources:
            limits:
              ephemeral-storage: 500M
              memory: 500Mi
            requests:
              memory: 100Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
            seccompProfile:
              type: RuntimeDefault
          volumeMounts:
            - name: public
              mountPath: /usr/src/app/public
      restartPolicy: Always
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: feemon
          maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
      volumes:
        - name: public
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: feemon
  annotations:
    kubernetes.io/ingress.class: nginx
  namespace: <environment>
spec:
  rules:
    - host: feemon.com
      http:
        paths:
          - backend:
              service:
                name: feemon3000
                port:
                  number: 3000
            path: /
            pathType: ImplementationSpecific
    - host: feemon.com
      http:
        paths:
          - backend:
              service:
                name: feemon80
                port:
                  number: 80
            path: /assets
            pathType: ImplementationSpecific
    - host: feemon.com
      http:
        paths:
          - backend:
              service:
                name: feemon80
                port:
                  number: 80
            path: /packs
            pathType: ImplementationSpecific
---
apiVersion: v1
kind: Service
metadata:
  name: feemon80
  namespace: <environment>
spec:
  ports:
    - port: 80
  selector:
    app: feemon
---
apiVersion: v1
kind: Service
metadata:
  name: feemon3000
  namespace: <environment>
spec:
  ports:
    - port: 3000
  selector:
    app: feemon