AWSTemplateFormatVersion: '2010-09-09'
Resources:
  MyDLQ:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: dev-payments-and-account-changes-dlq.fifo
      FifoQueue: true
      ContentBasedDeduplication: false
      DelaySeconds: 0
      MaximumMessageSize: 262144
      MessageRetentionPeriod: 1209600
      VisibilityTimeout: 30
      ReceiveMessageWaitTimeSeconds: 5
      DeduplicationScope: messageGroup
      FifoThroughputLimit: perMessageGroupId
    
  MySQS:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: dev-payments-and-account-changes.fifo
      FifoQueue: true
      ContentBasedDeduplication: false
      DelaySeconds: 0
      MaximumMessageSize: 262144
      MessageRetentionPeriod: 1209600
      VisibilityTimeout: 30
      ReceiveMessageWaitTimeSeconds: 5
      DeduplicationScope: messageGroup
      FifoThroughputLimit: perMessageGroupId
      RedrivePolicy: 
       deadLetterTargetArn: !GetAtt MyDLQ.Arn
       maxReceiveCount: 3

  MySQSPolicy:
    Type: AWS::SQS::QueuePolicy
    Properties:
      Queues:
        - Ref: MySQS
      PolicyDocument:
        Id: QueuePolicy
        Version: '2012-10-17'
        Statement:
          - Action:
              - sqs:SendMessage
              - sqs:ReceiveMessage
            Effect: Allow
            Resource: !GetAtt MySQS.Arn
            Principal:
              AWS: "arn:aws:iam::************:user/sqs-sns-user"

  MySQSPolicyDLQ:
    Type: AWS::SQS::QueuePolicy
    Properties:
      Queues:
        - Ref: MyDLQ
      PolicyDocument:
        Id: QueuePolicy
        Version: '2012-10-17'
        Statement:
          - Action:
              - sqs:SendMessage
              - sqs:ReceiveMessage
            Effect: Allow
            Resource: !GetAtt MyDLQ.Arn
            Principal:
              AWS: "arn:aws:iam::************:user/sqs-sns-user"
  
  MySNSTopicDLQ:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: dev-payments-and-account-changes-dlq
    
  MyAlarmDLQ:            
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: dev-payments-and-account-changes sqs dlq alarm
      ActionsEnabled: true
      OKActions: []
      AlarmActions:
          - !Ref MySNSTopicDLQ
      InsufficientDataActions: []
      MetricName: ApproximateNumberOfMessagesVisible
      Namespace: AWS/SQS
      Statistic: Average
      Dimensions:
          - Name: QueueName
            Value: !Ref MyDLQ.QueueName
      Period: 300
      EvaluationPeriods: 1
      DatapointsToAlarm: 1
      Threshold: 1
      ComparisonOperator: GreaterThanOrEqualToThreshold
      TreatMissingData: missing
      
  MySNSTopicDLQSubscription:
    Type: AWS::SNS::Subscription
    Properties:
      TopicArn: !Ref MySNSTopicDLQ
      Protocol: email
      Endpoint: <EMAIL>
         
  MySNSTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: dev-payments-and-account-changes
    
  MyAlarm:            
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: dev-payments-and-account-changes sqs alarm
      ActionsEnabled: true
      OKActions: []
      AlarmActions:
          - !Ref MySNSTopic
      InsufficientDataActions: []
      MetricName: ApproximateNumberOfMessagesVisible
      Namespace: AWS/SQS
      Statistic: Average
      Dimensions:
          - Name: QueueName
            Value: !Ref MySQS.QueueName
      Period: 300
      EvaluationPeriods: 1
      DatapointsToAlarm: 1
      Threshold: 100
      ComparisonOperator: GreaterThanOrEqualToThreshold
      TreatMissingData: missing
      
  MySNSTopicSubscription:
    Type: AWS::SNS::Subscription
    Properties:
      TopicArn: !Ref MySNSTopic
      Protocol: email
      Endpoint: <EMAIL>

