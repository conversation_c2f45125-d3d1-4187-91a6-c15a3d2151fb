AWSTemplateFormatVersion: '2010-09-09'
Resources:
  MyDLQ:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: staging-policy-data-print-dlq.fifo
      FifoQueue: true
      ContentBasedDeduplication: false
      DelaySeconds: 0
      MaximumMessageSize: 262144
      MessageRetentionPeriod: 1209600
      VisibilityTimeout: 30
      ReceiveMessageWaitTimeSeconds: 5
      DeduplicationScope: messageGroup
      FifoThroughputLimit: perMessageGroupId
    
  MySQS:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: staging-policy-data-print.fifo
      FifoQueue: true
      ContentBasedDeduplication: false
      DelaySeconds: 0
      MaximumMessageSize: 262144
      MessageRetentionPeriod: 1209600
      VisibilityTimeout: 30
      ReceiveMessageWaitTimeSeconds: 5
      DeduplicationScope: messageGroup
      FifoThroughputLimit: perMessageGroupId
      RedrivePolicy: 
       deadLetterTargetArn: !GetAtt MyDLQ.Arn
       maxReceiveCount: 3

  MySQSPolicy:
    Type: AWS::SQS::QueuePolicy
    Properties:
      Queues:
        - Ref: MySQS
      PolicyDocument:
        Id: QueuePolicy
        Version: '2012-10-17'
        Statement:
          - Action:
              - sqs:SendMessage
              - sqs:ReceiveMessage
            Effect: Allow
            Resource: !GetAtt MySQS.Arn
            Principal:
              AWS: "arn:aws:iam::765950528148:user/sqs-sns-user"
          - Sid: SnsSubscription
            Effect: Allow
            Principal:
              Service: sns.amazonaws.com
            Action: 'sqs:SendMessage'
            Resource: !GetAtt MySQS.Arn
            Condition:
              ArnEquals:
                'aws:SourceArn': 'arn:aws:sns:us-east-1:765950528148:staging-*'

  MySQSPolicyDLQ:
    Type: AWS::SQS::QueuePolicy
    Properties:
      Queues:
        - Ref: MyDLQ
      PolicyDocument:
        Id: QueuePolicy
        Version: '2012-10-17'
        Statement:
          - Action:
              - sqs:SendMessage
              - sqs:ReceiveMessage
            Effect: Allow
            Resource: !GetAtt MyDLQ.Arn
            Principal:
              AWS: "arn:aws:iam::765950528148:user/sqs-sns-user"
     
  MyAlarmDLQ:            
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: staging-policy-data-print sqs dlq alarm
      ActionsEnabled: true
      OKActions: []
      AlarmActions:
          - !Ref MySNSTopicDLQ
      InsufficientDataActions: []
      MetricName: ApproximateNumberOfMessagesVisible
      Namespace: AWS/SQS
      Statistic: Average
      Dimensions:
          - Name: QueueName
            Value: !GetAtt MyDLQ.QueueName
      Period: 300
      EvaluationPeriods: 1
      DatapointsToAlarm: 1
      Threshold: 1
      ComparisonOperator: GreaterThanOrEqualToThreshold
      TreatMissingData: missing
         
  MySNSTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: staging-policy-transaction-issued.fifo
      DisplayName: Policy transaction (new business, renewal, etc) issued/processed
      FifoTopic: true

  MySNSTopicDLQ:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: staging-policy-transaction-issued-dlq.fifo
      FifoTopic: true

  MyAlarm:            
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: staging-policy-data-print sqs alarm
      ActionsEnabled: true
      OKActions: []
      AlarmActions:
          - !Ref MySNSTopic
      InsufficientDataActions: []
      MetricName: ApproximateNumberOfMessagesVisible
      Namespace: AWS/SQS
      Statistic: Average
      Dimensions:
          - Name: QueueName
            Value: !GetAtt MySQS.QueueName
      Period: 300
      EvaluationPeriods: 1
      DatapointsToAlarm: 1
      Threshold: 100
      ComparisonOperator: GreaterThanOrEqualToThreshold
      TreatMissingData: missing
      
  MySNSTopicSubscription1:
    Type: AWS::SNS::Subscription
    Properties:
      TopicArn: !Ref MySNSTopic
      Protocol: SQS
      Endpoint: !GetAtt MySQS.Arn
      RawMessageDelivery: true

  MySNSTopicSubscription2:
    Type: AWS::SNS::Subscription
    Properties:
      TopicArn: !Ref MySNSTopic
      Protocol: SQS
      Endpoint: "arn:aws:sqs:us-east-1:765950528148:staging-policy-transaction-log.fifo"
      RawMessageDelivery: true

  MySNSTopicSubscription3:
    Type: AWS::SNS::Subscription
    Properties:
      TopicArn: !Ref MySNSTopicDLQ
      Protocol: SQS
      Endpoint: !GetAtt MyDLQ.Arn
      RawMessageDelivery: true
