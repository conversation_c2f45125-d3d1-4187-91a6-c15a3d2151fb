AWSTemplateFormatVersion: '2010-09-09'
Resources:   
  MySQS:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: staging-policy-transaction-log.fifo
      FifoQueue: true
      ContentBasedDeduplication: false
      DelaySeconds: 0
      MaximumMessageSize: 262144
      MessageRetentionPeriod: 259200
      VisibilityTimeout: 30
      ReceiveMessageWaitTimeSeconds: 5
      DeduplicationScope: messageGroup
      FifoThroughputLimit: perMessageGroupId

  MySQSPolicy:
    Type: AWS::SQS::QueuePolicy
    Properties:
      Queues:
        - Ref: MySQS
      PolicyDocument:
        Id: QueuePolicy
        Version: '2012-10-17'
        Statement:
          - Action:
              - sqs:SendMessage
              - sqs:ReceiveMessage
            Effect: Allow
            Resource: !GetAtt MySQS.Arn
            Principal:
              AWS: "arn:aws:iam::765950528148:user/sqs-sns-user"
          - Sid: SnsSubscription
            Effect: Allow
            Principal:
              Service: sns.amazonaws.com
            Action: 'sqs:SendMessage'
            Resource: !GetAtt MySQS.Arn
            Condition:
              ArnEquals:
                'aws:SourceArn': 'arn:aws:sns:us-east-1:765950528148:staging-*'

