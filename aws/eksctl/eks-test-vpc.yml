apiVersion: eksctl.io/v1alpha5
kind: ClusterConfig

metadata:
  name: vpc-test
  region: us-east-1
  version: "1.29"
vpc:
  id: vpc-0ac846058e8097ce2
  subnets:
    private:
      us-east-1a:
        id: subnet-019df20da9ec15591
      us-east-1b:
        id: subnet-01b9decce34eb4d95
    public:
      us-east-1a:
        id: subnet-0893068867675e89f
      us-east-1b:
        id: subnet-064508617defb92fd
managedNodeGroups:
  - name: managed-vpc
    instanceType: t3.large
    privateNetworking: true
    volumeSize: 20
    minSize: 2
    desiredCapacity: 2
    maxSize: 3
