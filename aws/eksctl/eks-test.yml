apiVersion: eksctl.io/v1alpha5
kind: ClusterConfig

metadata:
  name: eks-prod
  region: us-east-1

vpc:
  cidr: "10.1.0.0/16"

nodeGroups:
  - name: ng-prod
    instanceType: t3.large
    desiredCapacity: 3
    maxSize: 5
    iam:
      withAddonPolicies:
        autoScaler: true
    ssh:
      publicKeyName: EKS
managedNodeGroups:
  - name: managed-2
    instanceType: t3.large
    privateNetworking: true
    volumeSize: 20
    minSize: 2
    desiredCapacity: 3
    maxSize: 5
availabilityZones: ['us-east-1a', 'us-east-1b', 'us-east-1c', 'us-east-1d']