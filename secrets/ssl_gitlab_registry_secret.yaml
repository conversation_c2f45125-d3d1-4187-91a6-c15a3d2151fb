apiVersion: v1
data:
  .dockerconfigjson: eyJhdXRocyI6eyJyZWdpc3RyeS5pbXRpbnMuY29tOjUwMDUiOnsicGFzc3dvcmQiOiJ6eEV3VkxFMzNRVy00NTkyYXRLTiIsInVzZXJuYW1lIjoiY2FydGVyLm1pbmVhciJ9fX0=
kind: Secret
metadata:
  name: gitlab
  namespace: default
type: kubernetes.io/dockerconfigjson

---

apiVersion: v1
data:
  tls.crt: 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
  tls.key: 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
kind: Secret
metadata:
  name: newimtins2022
  namespace: default
type: kubernetes.io/tls
---

apiVersion: v1
data:
  .dockerconfigjson: eyJhdXRocyI6eyJyZWdpc3RyeS5pbXRpbnMuY29tOjUwMDUiOnsicGFzc3dvcmQiOiJ6eEV3VkxFMzNRVy00NTkyYXRLTiIsInVzZXJuYW1lIjoiY2FydGVyLm1pbmVhciJ9fX0=
kind: Secret
metadata:
  name: gitlab
  namespace: imt-platform
type: kubernetes.io/dockerconfigjson

---

apiVersion: v1
data:
  tls.crt: 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
  tls.key: 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
kind: Secret
metadata:
  name: newimtins2022
  namespace: imt-platform
type: kubernetes.io/tls

