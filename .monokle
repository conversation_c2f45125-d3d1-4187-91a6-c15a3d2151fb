{"scanExcludes": ["node_modules", "**/.git", "**/pkg/mod/**", "**/.kube", "**/*.swp", ".monokle"], "fileIncludes": ["*.yaml", "*.yml"], "folderReadsMaxDepth": 10, "k8sVersion": "1.26.0", "settings": {"helmPreviewMode": "template", "kustomizeCommand": "kubectl", "createDefaultObjects": false, "setDefaultPrimitiveValues": true, "allowEditInClusterMode": true}, "kubeConfig": {"path": "/Users/<USER>/.kube/config", "currentContext": "arn:aws:eks:us-east-1:765950528148:cluster/eks-production"}}