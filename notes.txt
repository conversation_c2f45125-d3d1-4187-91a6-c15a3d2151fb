DEPLOY:
kubectl apply -f <deploy.yaml>
kubectl expose deployment <deploy> --name=<name> --port=80,3000

RUN TERMINAL IN CLUSTER:
kubectl run --generator=run-pod/v1 busybox --rm -ti --image=busybox -- /bin/sh

GET BACKED OFF IMAGE PREVIOUS RUN LOGS
kubectl logs <deploy name> -p

GET EVENTS SORTED
kubectl get events  --sort-by='.metadata.creationTimestamp'  -o 'go-template={{range .items}}{{.involvedObject.name}}{{"\t"}}{{.involvedObject.kind}}{{"\t"}}{{.message}}{{"\t"}}{{.reason}}{{"\t"}}{{.type}}{{"\t"}}{{.firstTimestamp}}{{"\n"}}{{end}}'

CREATE SECRET
kubectl create secret docker-registry gitlab2 --docker-server registry.gitlab.com --docker-username=gitlab-ci-token --docker-password=-s1txXFE_XgjCQkaum8J --docker-email=<EMAIL>
kubectl create secret docker-registry imt --docker-server=registry.imtins.com:5005 --docker-username=hobbs --docker-password=weCcfDzD6LMEEfUof3ui --docker-email=<EMAIL>

CONNECT
kubectl exec -it postgres-8cb4cc6d4-rdqd8 -- /bin/bash

HELM
brew install helm
helm repo add stable https://kubernetes-charts.storage.googleapis.com/
helm search repo stable

INGRESS
helm install stable/nginx-ingress --generate-name --set controller.publishService.enabled=true

GET ENDPOINTS
kubectl get ep <service>

CORE-DNS
kubectl get services kube-dns --namespace=kube-system

NSLOOKUP
kubectl run curl --image=radial/busyboxplus:curl -i --tty

REDEPLOY
kubectl patch deployment <name> -p "{\"spec\": {\"template\": {\"metadata\": { \"labels\": {  \"redeploy\": \"$(date +%s)\"}}}}}"

GET CLUSTER CONTEXTS
kubectl config get-contexts

CHANGE CONTEXTS
kubectl config use-context CONTEXT_NAME

ADD CONTEXT
aws eks --region us-east-1 update-kubeconfig --name test-cluster-fargate

ADD AUTHORITY TO AWS CLUSTER
eksctl create iamidentitymapping --cluster <cluster-name> --arn arn:aws:iam::<id>:user/<user-name> --group system:masters --username ops-user

DELETE PODS
kubectl delete --all pods --namespace=insights-agent




NEW EKS CLUSTER
eksctl create cluster -f eks-test.yml
eksctl utils associate-iam-oidc-provider --cluster=<cluster> --region us-east-1 --approve
eksctl create iamserviceaccount \
--name ebs-csi-vpc-test \
--namespace kube-system \
--cluster vpc-test \
--role-name AmazonEKS_EBS_CSI_DriverRole_vpc_test \
--role-only \
--attach-policy-arn arn:aws:iam::aws:policy/service-role/AmazonEBSCSIDriverPolicy \
--approve
add addon Amazon VPC CNI
add addon Amazon EBS CSI Driver (use role created above)
