#!/bin/bash
### Set Language
TEXTDOMAIN=K8sDeployYamls

### Set default parameters
yamlPath="/Users/<USER>/projects/k8s_manifest/deploy-yamls/"
yamlFilename=$1
environment=$(echo "$2" | tr '[:upper:]' '[:lower:]')
replicas=${3:-1}  # Default to 1 if no replicas value is given
process_all() {
    for file in "$yamlPath"/*.yaml; do
        if [ -f "$file" ]; then
            echo "Processing $file for environment $environment with $replicas replicas"
            if [ "$environment" != 'production' ]; then
                sed "s/-<environment>/-$environment/ ; s/<environment>/$environment/ ; s/<replica>/$replicas/" "$file" | kubectl apply -f -
            else
                sed "s/-<environment>// ; s/<environment>/$environment/ ; s/<replica>/$replicas/" "$file" | kubectl apply -f -
            fi
        fi
    done
}

if [[ $yamlFilename == *.yaml ]] || [ "$yamlFilename" == 'all' ]; then
	yamlFilename=$yamlFilename
else
	yamlFilename+=".yaml"
fi

if [ "$yamlFilename" != "all" ] && [ ! -f "$yamlPath$yamlFilename" ]; then
    echo "Error: File $yamlFilename does not exist."
    exit 1
fi

# Extract the 'allowedEnvironments' string from the YAML file
allowed_environments=$(grep "allowedEnvironments:" "$yamlPath$yamlFilename" | sed 's/allowedEnvironments: //')

# Check if the 'allowedEnvironments' string is empty or missing
if [ -z "$allowed_environments" ]; then
    echo "No 'allowedEnvironments' found in the YAML file."
    exit 1
fi

# Convert the comma-separated string into an array
IFS=',' read -ra env_array <<< "$allowed_environments"

# Search for the environment in the array
found=false
for env in "${env_array[@]}"; do
    # Trim spaces around elements and check for match
    trimmed_env=$(echo "$env" | xargs)  # xargs trims leading/trailing spaces
    if [[ "$trimmed_env" == "$environment" ]]; then
        found=true
        break
    fi
done

if [ "$found" = true ]; then
    echo "Found '$environment' in the 'allowedEnvironments' array."
else
    echo "'$environment' not found in the 'allowedEnvironments' array."
    exit 1
fi


if [[ "$environment" =~ ^(dev|staging|qa|clone|default)$ ]]; then
    if [ "$yamlFilename" == 'all' ]; then
        process_all
    else
        echo "Processing $yamlFilename for environment $environment with $replicas replicas"
        sed "s/-<environment>/-$environment/ ; s/<environment>/$environment/ ; s/<replica>/$replicas/" "$yamlPath$yamlFilename" | kubectl apply -f -
    fi
elif [ "$environment" == 'production' ]; then
    if [ "$yamlFilename" == 'all' ]; then
        process_all
    else
        echo "Processing $yamlFilename for production environment with $replicas replicas"
        sed "s/-<environment>// ; s/<environment>/$environment/ ; s/<replica>/$replicas/" "$yamlPath$yamlFilename" | kubectl apply -f -
    fi
else
    echo "Error: Enter a valid environment: dev, staging, qa, clone, or production."
    exit 1
fi
